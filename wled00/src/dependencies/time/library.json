{"name": "Time", "description": "Time keeping library", "keywords": "Time, date, hour, minute, second, day, week, month, year, RTC", "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.pjrc.com", "maintainer": true}], "repository": {"type": "git", "url": "https://github.com/PaulStoffregen/Time"}, "version": "1.5", "homepage": "http://playground.arduino.cc/Code/Time", "frameworks": "<PERSON><PERSON><PERSON><PERSON>", "examples": ["examples/*/*.ino"]}