<!DOCTYPE html>
<html lang="en">
<head>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
	<meta charset="utf-8">
	<title>Sync Settings</title>
	<script src="common.js" async type="text/javascript"></script>
	<script>
	function adj(){if (d.Sf.DI.value == 6454) {if (d.Sf.EU.value == 1) d.Sf.EU.value = 0;}
					else if (d.Sf.DI.value == 5568) {if (d.Sf.DA.value == 0) d.Sf.DA.value = 1; if (d.Sf.EU.value == 0) d.Sf.EU.value = 1;} }
	function FC()
	{
		for(j=0;j<8;j++)
		{
			gId("G"+(j+1)).checked=gId("GS").value>>j&1;
			gId("R"+(j+1)).checked=gId("GR").value>>j&1;
		}
	}
	function GC()
	{
		var a=0, b=0;

		var m=1;
		for(j=0;j<8;j++)
		{
			a+=gId("G"+(j+1)).checked*m;
			b+=gId("R"+(j+1)).checked*m;
			m*=2;
		}
		gId("GS").value=a;
		gId("GR").value=b;
	}
	function SP(){var p = d.Sf.DI.value; gId("xp").style.display = (p > 0)?"none":"block"; if (p > 0) d.Sf.EP.value = p;}
	function SetVal(){switch(parseInt(d.Sf.EP.value)){case 5568: d.Sf.DI.value = 5568; break; case 6454: d.Sf.DI.value = 6454; break; case 4048: d.Sf.DI.value = 4048; break; }; SP();FC();}
	function S(){
		getLoc();
		loadJS(getURL('/settings/s.js?p=4'), false, undefined, ()=>{SetVal();});	// If we set async false, file is loaded and executed, then next statement is processed
		if (loc) d.Sf.action = getURL('/settings/sync');
	}
	function getURL(path) {
		return (loc ? locproto + "//" + locip : "") + path;
	}
	function hideDMXInput(){gId("dmxInput").style.display="none";}
	function hideNoDMXInput(){gId("dmxInputOff").style.display="none";}
	</script>
	<style>@import url("style.css");</style>
</head>
<body onload="S()">
<form id="form_s" name="Sf" method="post" onsubmit="GC()">
<div class="toprow">
<div class="helpB"><button type="button" onclick="H('interfaces/udp-notifier/')">?</button></div>
<button type="button" onclick="B()">Back</button><button type="submit">Save</button><hr>
</div>
<h2>Sync setup</h2>
<h3>WLED Broadcast</h3>
UDP Port: <input name="UP" type="number" min="1" max="65535" class="d5" required><br>
2nd Port: <input name="U2" type="number" min="1" max="65535" class="d5" required><br>
<div id="NoESPNOW" class="hide">
<i class="warn">ESP-NOW support is disabled.<br></i>
</div>
<div id="ESPNOW">
Use ESP-NOW sync: <input type="checkbox" name="EN"><br><i>(in AP mode or no WiFi)</i><br>
</div>
<h3>Sync groups</h3>
<input name="GS" id="GS" type="number" style="display: none;"><!-- hidden inputs for bitwise group checkboxes -->
<input name="GR" id="GR" type="number" style="display: none;">
<table style="margin: 0 auto;">
	<tr>
		<td></td>
		<td>1</td>
		<td>2</td>
		<td>3</td>
		<td>4</td>
		<td>5</td>
		<td>6</td>
		<td>7</td>
		<td>8</td>
	</tr>
	<tr>
		<td>Send:</td>
		<td><input type="checkbox" id="G1" name="G1"></td>
		<td><input type="checkbox" id="G2" name="G2"></td>
		<td><input type="checkbox" id="G3" name="G3"></td>
		<td><input type="checkbox" id="G4" name="G4"></td>
		<td><input type="checkbox" id="G5" name="G5"></td>
		<td><input type="checkbox" id="G6" name="G6"></td>
		<td><input type="checkbox" id="G7" name="G7"></td>
		<td><input type="checkbox" id="G8" name="G8"></td>
	</tr>
	<tr>
		<td>Receive:</td>
		<td><input type="checkbox" id="R1" name="R1"></td>
		<td><input type="checkbox" id="R2" name="R2"></td>
		<td><input type="checkbox" id="R3" name="R3"></td>
		<td><input type="checkbox" id="R4" name="R4"></td>
		<td><input type="checkbox" id="R5" name="R5"></td>
		<td><input type="checkbox" id="R6" name="R6"></td>
		<td><input type="checkbox" id="R7" name="R7"></td>
		<td><input type="checkbox" id="R8" name="R8"></td>
	</tr>
</table>
<h3>Receive</h3>
<nowrap><input type="checkbox" name="RB">Brightness,</nowrap> <nowrap><input type="checkbox" name="RC">Color,</nowrap> <nowrap><input type="checkbox" name="RX">Effects,</nowrap> <nowrap>and <input type="checkbox" name="RP">Palette</nowrap><br>
<input type="checkbox" name="SO"> Segment options, <input type="checkbox" name="SG"> bounds
<h3>Send</h3>
Enable Sync on start: <input type="checkbox" name="SS"><br>
Send notifications on direct change: <input type="checkbox" name="SD"><br>
Send notifications on button press or IR: <input type="checkbox" name="SB"><br>
Send Alexa notifications: <input type="checkbox" name="SA"><br>
Send Philips Hue change notifications: <input type="checkbox" name="SH"><br>
UDP packet retransmissions: <input name="UR" type="number" min="0" max="30" class="d5" required><br><br>
<i>Reboot required to apply changes. </i>
<hr class="sml">
<h3>Instance List</h3>
Enable instance list: <input type="checkbox" name="NL"><br>
Make this instance discoverable: <input type="checkbox" name="NB">
<hr class="sml">
<h3>Realtime</h3>
Receive UDP realtime: <input type="checkbox" name="RD"><br>
Use main segment only: <input type="checkbox" name="MO"><br>
Respect LED Maps: <input type="checkbox" name="RLM"><br><br>
<i>Network DMX input</i><br>
Type:
<select name=DI onchange="SP(); adj();">
<option value=5568>E1.31 (sACN)</option>
<option value=6454>Art-Net</option>
<option value=0 selected>Custom port</option>
</select><br>
<div id=xp>Port: <input name="EP" type="number" min="1" max="65535" value="5568" class="d5" required><br></div>
Multicast: <input type="checkbox" name="EM"><br>
Start universe: <input name="EU" type="number" min="0" max="63999" required><br>
<i>Reboot required.</i> Check out <a href="https://github.com/LedFx/LedFx" target="_blank">LedFx</a>!<br>
Skip out-of-sequence packets: <input type="checkbox" name="ES"><br>
DMX start address: <input name="DA" type="number" min="1" max="510" required><br>
DMX segment spacing: <input name="XX" type="number" min="0" max="150" required><br>
E1.31 port priority: <input name="PY" type="number" min="0" max="200" required><br>
DMX mode:
<select name=DM>
<option value=0>Disabled</option>
<option value=1>Single RGB</option>
<option value=2>Single DRGB</option>
<option value=3>Effect</option>
<option value=7>Effect + White</option>
<option value=8>Effect Segment</option>
<option value=9>Effect Segment + White</option>
<option value=4>Multi RGB</option>
<option value=5>Dimmer + Multi RGB</option>
<option value=6>Multi RGBW</option>
<option value=10>Preset</option>
</select><br>
<a href="https://kno.wled.ge/interfaces/e1.31-dmx/" target="_blank">E1.31 info</a><br>
Timeout: <input name="ET" type="number" min="1" max="65000" required> ms<br>
Force max brightness: <input type="checkbox" name="FB"><br>
Disable realtime gamma correction: <input type="checkbox" name="RG"><br>
Realtime LED offset: <input name="WO" type="number" min="-255" max="255" required>
<div id="dmxInput">
	<h4>Wired DMX Input Pins</h4>
	DMX RX: <input name="IDMR" type="number" min="-1" max="99">RO<br/>
	DMX TX: <input name="IDMT" type="number" min="-1" max="99">DI<br/>
	DMX Enable: <input name="IDME" type="number" min="-1" max="99">RE+DE<br/>
	DMX Port: <input name="IDMP" type="number" min="1" max="2"><br/>
</div> 
<div id="dmxInputOff">
	<br><em style="color:darkorange">This firmware build does not include DMX Input support. <br></em>
</div> 
<div id="dmxOnOff2">
  <br><em style="color:darkorange">This firmware build does not include DMX output support. <br></em>
</div> 
<hr class="sml">
<h3>Alexa Voice Assistant</h3>
<div id="NoAlexa" class="hide">
	<i class="warn">This firmware build does not include Alexa support.<br></i><br>
</div>
<div id="Alexa">
Emulate Alexa device: <input type="checkbox" name="AL"><br>
Alexa invocation name: <input type="text" name="AI" maxlength="32"><br>
Also emulate devices to call the first <input name="AP" type="number" class="s" min="0" max="9"> presets<br><br>
</div>
<hr class="sml">
<div class="warn">&#9888; <b>MQTT and Hue sync all connect to external hosts!<br>
This may impact the responsiveness of WLED.</b><br>
</div>
For best results, only use one of these services at a time.<br>
(alternatively, connect a second ESP to them and use the UDP sync)
<hr class="sml">
<h3>MQTT</h3>
<div id="NoMQTT" class="hide">
	<i class="warn">This firmware build does not include MQTT support.<br></i>
</div>
<div id="MQTT">
Enable MQTT: <input type="checkbox" name="MQ"><br>
Broker: <input type="text" name="MS" maxlength="32">
Port: <input name="MQPORT" type="number" min="1" max="65535" class="d5"><br>
<b>The MQTT credentials are sent over an unsecured connection.<br>
Never use the MQTT password for another service!</b><br>
Username: <input type="text" name="MQUSER" maxlength="40"><br>
Password: <input type="password" name="MQPASS" maxlength="64"><br>
Client ID: <input type="text" name="MQCID" maxlength="40"><br>
Device Topic: <input type="text" name="MD" maxlength="32"><br>
Group Topic: <input type="text" name="MG" maxlength="32"><br>
Publish on button press: <input type="checkbox" name="BM"><br>
Retain brightness & color messages: <input type="checkbox" name="RT"><br>
<i>Reboot required to apply changes. </i><a href="https://kno.wled.ge/interfaces/mqtt/" target="_blank">MQTT info</a>
</div>
<h3>Philips Hue</h3>
<div id="NoHue" class="hide">
	<em class="warn">This firmware build does not include Philips Hue support.<br></em>
</div>
<div id="Hue">
<i>You can find the bridge IP and the light number in the 'About' section of the hue app.</i><br>
Poll Hue light <input name="HL" type="number" min="1" max="99" > every <input name="HI" type="number" min="100" max="65000"> ms: <input type="checkbox" name="HP"><br>
Then, receive <input type="checkbox" name="HO"> On/Off, <input type="checkbox" name="HB"> Brightness, and <input type="checkbox" name="HC"> Color<br>
Hue Bridge IP:<br>
<input name="H0" type="number" class="s" min="0" max="255" > .
<input name="H1" type="number" class="s" min="0" max="255" > .
<input name="H2" type="number" class="s" min="0" max="255" > .
<input name="H3" type="number" class="s" min="0" max="255" ><br>
<b>Press the pushlink button on the bridge, after that save this page!</b><br>
(when first connecting)<br>
Hue status: <span class="sip"> Disabled in this build </span>
</div>
<h3>Serial</h3>
<div id="NoSerial" class="hide">
	<em class="warn">This firmware build does not support Serial interface.<br></em>
</div>
<div id="Serial">
Baud rate:
<select name=BD>
<option value=1152>115200</option>
<option value=2304>230400</option>
<option value=4608>460800</option>
<option value=5000>500000</option>
<option value=5760>576000</option>
<option value=9216>921600</option>
<option value=10000>1000000</option>
<option value=15000>1500000</option>
</select><br>
<i>Keep at 115200 to use Improv. Some boards may not support high rates.</i>
</div>
<hr>
<button type="button" onclick="B()">Back</button><button type="submit">Save</button>
</form>
</body>
</html>