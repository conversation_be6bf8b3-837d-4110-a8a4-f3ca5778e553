@font-face {
	font-family: "WIcons";
	src: url(data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAnUAAsAAAAAE1AAAAmFAAGZmgAAAAAAAAAAAAAAAAAAAAAAAAAABmAAgXwRCAqcYJZIATYCJANwCzoABCAFgwYHIBs7D8iOwzgm3MXMnzZCktnjcbN+QlJLaJ3ulULplpW6UqWioeS91Jye0jUlJwZr5nTdE3LntdPvAg+ft/fbsLsGlNLuhlmQjKi7NPDEIgwTmP//a6mdl+SHUBhEIdHFxak7s4E/yzhJSjC7BQQLfDwopF/i6aqSElEFDXx8ZVWjy3rym4N6FlZQ4hu+nXsGIDMQF3gAxa14AgArtVMhfkgjfEAbiChwuSIwEUCmudPhiQdT6rvIjLSRZEwDhF9BIsooI53TIRIoIUD8kyNZI7UjAyMrR/aM/DwaOpozah9LGCsY2zN2YOzs2L3xqeNp4zXjq8bXT/hMBLj/53YDAIS+7u668n3H+HRPdZd1u3TzdRZdVMTfIl5HfKgd1b7Svqd9W9uprdP8QTOmeaz5TPORJlDDjHVjG0ANMQYsmRrKlmpyqV7kubIQC2GSIkFS+MneCJ48JJFVChQfuwKMp2yU9pmq1VKUR6ret0Gp0SjVYRRF+Xj7+OiUSk/GIzu1miHZWx+g8Y1RUktPmqIitRTXVNzzCtuFPKcH0zRBG+Y9/CnhBa20v5oHfsEUMgXMPEfO5ZcJx0FIPiVywgjb6MIuV+oZ4v2kk6/znIxDKrguM22y+bW8wUGqi7aL8fQJzwnCj8tIppdI9bYDSVJVCQInipW0HbtclcT7vCyLmXaSVrQSNMybaJJBh2PiXrXbgd6AbqecdDTO9EQEIeW0VPWQcdQ8ltPOEu+76q2IxUToJeWpfjQiHHH5AsADLj1bHgQxXsUoHfKYbg+CxCxC69eHcOvWheJ1l6b0nD7jG+bSA1dCZVxmw8ZJ/IYtxPtbJxlpQ/LGjSq00TmdNIZxrGel+y+rZJro+nUh3PrNIGwK6WrXNMV2xTeRWHSjScktLJfe1rc7spyvk3b6V4k48Sr3Am1Pv/QifhsI2uMvc863OiQQRNoedpPfHnSwcete+aDEE67cKzTgBlQgjpjgTDnJtGnX2qbmXJ6FOBLZ7wsr+JZzYnbjdbkCuEfU0HvlwqbtUgJ7zRXFNJsvSxlwz2WYta4xjri/fsulnnFVPyonpP0RL5oVNKkkfElG4csTDNAsgzC38G7gSKVgSZ7m/cEvKALmxKz//u7h6egHF7MrH4jJp/Zx4q32a8T71xnHVRCGlfFZNttd2FcUaay6e9PkhucyR0oPu1z1z/DB+8wixAFdMU1gnmB4xAw68pwHcWjlFrBnXxLjj63UGgvNGVGAJFzxFw+Womn7MAibVbu6leHRB5sc10fLtbrdr/JqV6Yr+ovwFtRHE7M4zG90qNB6YREoo51kFJabq3NeHVKdef/hsMFFSpt5m8XmJqDDAnR0c418mxmxrQzQuyPnspRwfAYkpthzr7gST1xNSf4WtBMM9DQT19uL+gb47gFLP3cT08F8I4dZxJl41Gsx9WHzLBOHzWjRS9NLCOUBCFQ+uGhB/V7ZzUwKESTmDriJ+UecdD/bFXFMLLsjgiAt4pp7ulpxb2tzE8I8xhyHODBK3SGg6QP12BiP3YMw2rDFtWUDXL+esnv3H9QxqfmbDnbMLjGUFpqqZbnWSg0lhWv9wU35qTHqP9zqUrL7kqKj8YjZzg01pb9+yQ8sXZpYxKGiFJTNsIwwpyR44gEOnV/+ennFdHD/2lQ3uS5y1qzIztXUNPE6odYJ0PqUiWJtgKGKMILY60dxeYynbb+sFKKqNn0Wz2rLtMbBQWPnYtmJa4WqFRob/9mmuycQVv7ifCNvXrlhzgDLDvAGA+8H5xjK948cDet+FaXfS+Lko/Wt+vScqarq6kZTbk4NaKqpObkEEpsac9L1rRNXJgPbrWyDdYje6tBQAztkbYC0wDe4UnNipmnZtInu/ujf6Kf7ve112Huf92Ev/7enB/+nP7pbrPiQJZbi0jCSpoN9UNPTkj7JMwpbWgopAbhtbOWkytAF3K+/qo0SASNW2G2bLfnshpB4a9dmz7/Hx//dc3OXNZ46YRyXUV2dYRsD97qKL79qazu+vSI1vPXT7375bWSGocBofD2eIRzJ0cMC0tenwQ0gfvuSdvd14f1uEooLPE3JJHL6uCd/n5n8d35UOKPn6nhr8kyrV3ad3nz2iTiNL414EnefL/JGLlWZtZWaqoEh4xSjvsGb/6m9raFlsLm4uHkQWlv7T/weZzjHHe7xZiUzpJ5WAWBLDNwRKxwRYnFoXGxcaKxN6DR8BNn2o9Nqmmutvra5TnIjXMBlmIFZ3yPYX3Mt9v5mmHuwYvvxPverL9eSvszXNjUXrkbqcGOVW2bEbDGKi3MLVTWzzWHF54Bu/2rA1qko6l9fFgVbBurfVBWFFlVW1ugxOwcs+8W//FcUZJieLl9WXA8eGL5crB7fhOMyxl8bjQWGjB1bW/ok6Ucqensr7F8H7utsmdqoHmz99rvyeE/Pz7u64mvVXLjyY8v8j5XhZeH3aPX75dpiO5eN/OzwcG7zkflt/sd5e7YcqbOowfRg22R5585at2vXX87W1Y0gQ079497eYT1EkyoEqMYABmHd8QvKGrRG6bJYTDCCZYGEWcm5G1jXM2i54Y9WtiBuklP57YtBZMAWlu2fYzDM7Q+5FmxKS3Oz5jwK6IactbWPowuQgNyHluKlaw9wnbOmtuajo/VSw9FrBSRwMcuUV2ZwFhh6s7hsqriWCsgA2s3nFcri4I7O+asxwxZbtLL03E9bhcR6Yz9mIbF0U96K0xGA7bx9y+l2//73j+H2i0EGd27uAVNI/WhCYuWqIDaYxads0lcVFV+dOlHmBx/qO7c6/uZX0tReUtJQv64y3adAvX6xDezAX/8Wm8Cgh/95O9OxsNCYnsXWQ+7pCz8/NMZ57ZAIGEdTw+ap8V+I3NUVe375wiv+lccqj172X7Yw5gJAUQGYPQ6QyxRfgeC+Qc5WnAMCAHFv6TJtet3pn/83b4YCAIBv35ofpTRyt5PjZEwT8KYAEQK8nFgBcE/yUwn2oqHSBKoEG7KZQLMpjo5uha/PI2yuBWOCTSDZajpqQ68+Za18jgGgYMT8nBhjKcFrKCYF6yKSZRLF5tR5YKhUzzNWM52mBvuPMiL7xPx4UaRgFiJZAVFscZ2HUIhUPcEaH5WWDvvmvdPfl5KaCvO8o1+fFCBb6hvuLz8lMROwfjPN8iar90RCCiRCJr3ugqHf6LqgUYYs5hzvu9tMIOUr/xpvRsNVvdZ/p+mB8n7V2Spo0T+aRhPpNhsNFOqxoE2u0suqTipgx58IJA0AAAA=) format('woff');
}

:root {
	--c-1: #111;
	--c-f: #fff;
	--c-2: #222;
	--c-3: #333;
	--c-4: #444;
	--c-5: #555;
	--c-6: #666;
	--c-8: #888;
	--c-b: #bbb;
	--c-c: #ccc;
	--c-e: #eee;
	--c-d: #ddd;
	--c-r: #c32;
	--c-g: #2c1;
	--c-l: #48a;
	--c-y: #a90;
	--t-b: .5;
	--c-o: rgba(34, 34, 34, .9);
	--c-tb : rgba(34, 34, 34, var(--t-b));
	--c-tba: rgba(102, 102, 102, var(--t-b));
	--c-tbh: rgba(51, 51, 51, var(--t-b));
	/*following are internal*/
	--th: 70px;
	--tp: 70px;
	--bh: 63px;
	--tbp: 14px 14px 10px 14px;
	--bbp: 9px 0 7px 0;
	--bhd: none;
	--sgp: "block";
	--bmt: 0;
	--sti: 42px;
	--stp: 42px;
}

html {
	touch-action: manipulation;
}

body {
	margin: 0;
	background-color: var(--c-1);
	font-family: Helvetica, Verdana, sans-serif;
	font-size: 17px;
	color: var(--c-f);
	text-align: center;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: transparent;
	scrollbar-width: 6px;
	scrollbar-color: var(--c-sb) transparent;
}

html,
body {
	height: 100%;
	width: 100%;
	position: fixed;
	overscroll-behavior: none;
}

#bg {
	height: 100vh;
	width: 100vw;
	position: fixed;
	z-index: -10;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
	opacity: 0;
	transition: opacity 2s;
}

p {
	margin: 10px 0 2px 0;
}
a, p, a:visited {
	color: var(--c-d);
}
a, a:visited {
	text-decoration: none;
}

button {
	outline: 0;
	cursor: pointer;
}

.labels {
	margin: 0;
	padding: 8px 0 2px 0;
	font-size: 19px;
}

#namelabel {
	position: fixed;
	bottom: calc(var(--bh) + 6px);
	right: 6px;
	color: var(--c-8); /* set bright (--c-d) with dark text shadow (see below) to be legible on gray background (in image) */
	cursor: pointer;
	writing-mode: vertical-rl;
	/* transform: rotate(180deg); */
}

.bri {
	padding: 4px;
}

.wrapper {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: var(--c-tb);
	z-index: 1;
}

.icons {
	font-family: 'WIcons';
	font-style: normal;
	font-size: 24px !important;
	line-height: 1 !important;
	display: inline-block;
}

.on {
	color: var(--c-g) !important;
}

.off {
	color: var(--c-6) !important;
	/* cursor: default !important; */
}

.top .icons, .bot .icons {
	margin: -2px 0 4px 0;
}

.huge {
	font-size: 60px !important;
}

.segt, .plentry TABLE {
	table-layout: fixed;
	width: 100%;
}

.segt TD {
	padding: 2px 0 !important;
	text-align: center;
	/*text-transform: uppercase;*/
}
.segt TD, .plentry TD {
	font-size: 13px;
	padding: 0;
	vertical-align: middle;
}

.keytd {
	text-align: left;
}

.valtd {
	text-align: right;
}

.valtd i {
	font-size: small;
}

.slider-icon {
	position: absolute;
	left: 8px;
	bottom: 5px;
}

.sel-icon {
	transform: translateX(3px);
}

.e-icon, .g-icon, .sel-icon, .slider-icon {
	cursor: pointer;
	color: var(--c-d);
}

.g-icon {
	font-style: normal;
	position: absolute;
	top: 8px;
	right: 8px;
}

/* pop-up container */
.pop {
	position: absolute;
	display: inline-block;
	top: 0;
	right: 0;
}

/* pop-up content (segment sets) */
.pop-c {
	position: absolute;
	background-color: var(--c-2);
	border: 1px solid var(--c-8);
	border-radius: 20px;
	z-index: 1;
	top: 3px;
	right: 35px;
	padding: 3px 8px 1px;
	font-size: 24px;
	line-height: 24px;
}
.pop-c span {
	padding: 2px 6px;
}

.search-icon {
	position: absolute;
	top: 8px;
	left: 12px;
	width: 24px;
	height: 24px;
}

.clear-icon {
	position: absolute;
	top: 8px;
	right: 9px;
	cursor: pointer;
}

.flr {
	color: var(--c-f);
	transform: rotate(0deg);
	transition: transform .3s;
	position: absolute;
	top: 0;
	right: 0;
	padding: 8px;
}

.expanded .flr,
.exp {
	transform: rotate(180deg);
}

.il {
	display: inline-block;
	vertical-align: middle;
}

#liveview {
	height: 4px;
	width: 100%;
	border: 0;
}

#liveview2D {
	height: 90%;
	width: 90%;
	border: 0;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%,-50%);
}

.tab {
	background-color: transparent;
	color: var(--c-d);
}

.bot {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: var(--c-tb);
}

.tab button {
	background-color: transparent;
	float: left;
	border: 0;
	transition: color .3s, background-color .3s;
	font-size: 17px;
	color: var(--c-c);
	min-width: 44px;
}

.top button {
	padding: var(--tbp);
	margin: 0;
}

.bot button {
	padding: var(--bbp);
	width: 25%;
	margin: 0;
}

.tab button:hover {
	background-color: var(--c-tbh);
	color: var(--c-e);
}

.tab button.active {
	background-color: var(--c-tba) !important;
	color: var(--c-f);
}

.active {
	background-color: var(--c-6) !important;
	color: var(--c-f);
}

.container {
	--n: 1;
	width: 100%;
	width: calc(var(--n)*100%);
	height: calc(100% - var(--tp) - var(--bh));
	margin-top: var(--tp);
	transform: translate(calc(var(--i, 0)/var(--n)*-100%));
	overscroll-behavior: none;
}

.tabcontent {
	float: left;
	position: relative;
	width: 100%;
	width: calc(100%/var(--n));
	box-sizing: border-box;
	border: 0;
	overflow-y: auto;
	overflow-x: hidden;
	height: 100%;
	overscroll-behavior: none;
	padding: 0 4px;
	-webkit-overflow-scrolling: touch;
}

#Segments, #Presets, #Effects, #Colors {
	font-size: 19px;
	padding: 4px 0 0;
}

#segutil, #segutil2, #segcont, #putil, #pcont, #pql, #fx, #palw, #bsp,
.fnd {
	max-width: 280px;
}

#putil, #segutil, #segutil2, #bsp {
	min-height: 42px;
	margin: 0 auto;
}

#segutil .segin {
	padding-top: 12px;
}

#fx, #pql, #segcont, #pcont, #sliders, #qcs-w, #hexw, #pall, #ledmap,
.slider, .filter, .option, .segname, .pname, .fnd {
	margin: 0 auto;
}

#putil {
	padding: 5px 0 0;
}

/* Quick load magin for simplified UI */
.simplified #pql, .simplified #palw, .simplified #fx {
	margin-bottom: 8px;
}

.smooth { transition: transform	calc(var(--f, 1)*.5s) ease-out }

.tab-label {
	margin: 0 0 -5px 0;
	padding-bottom: 4px;
	display: var(--bhd);
}

.overlay {
	position: fixed;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
	background-color: var(--c-3);
	font-size: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 11;
	opacity: .95;
	transition: .7s;
	pointer-events: none;
}

.staytop, .staybot {
	display: block;
	position: -webkit-sticky;
	position: sticky !important;
	top: 0;
	z-index: 2;
	margin: 0 auto auto;
}

.staybot {
	bottom: 5px;
}

#sliders {
	position: -webkit-sticky;
	position: sticky;
	bottom: 0;
	max-width: 300px;
	z-index: 2;
}

#sliders .labels {
	padding-top: 3px;
	font-size: small;
}

.slider {
	/*max-width: 300px;*/
	/* margin: 5px auto; add 5px; if you want some vertical space but looks ugly */
	border-radius: 24px;
	position: relative;
	padding-bottom: 2px;
}

/* Slider wrapper div */
.sliderwrap {
	height: 30px;
	width: 230px;
	max-width: 230px;
	position: relative;
	z-index: 0;
}

#sliders .slider {
	padding-right: 64px; /* offset for bubble */
}

#sliders .slider, #info .slider {
	background-color: var(--c-2);
}

#sliders .sliderwrap, .sbs .sliderwrap {
	left: 32px; /* offset for icon */
}

.filter, .option {
	background-color: var(--c-4);
	border-radius: 26px;
	height: 26px;
	max-width: 300px;
	/* margin: 0 auto 4px; add 4-8px if you want space at the bottom */
	padding: 4px 2px;
	position: relative;
	opacity: 1;
	transition: opacity .25s linear, height .2s, transform .2s;
}

.filter {
	z-index: 1;
	/*overflow: visible;*/
	border-radius: 0 0 16px 16px;
	max-width: 220px;
	height: 54px;
	line-height: 1.5;
	padding-bottom: 8px;
	pointer-events: none;
}

/* New tooltip */
.tooltip {
	position: absolute;
	opacity: 0;
	visibility: hidden;
	transition: opacity .25s ease, visibility .25s ease;
	background-color: var(--c-5);
	box-shadow: 4px 4px 10px 4px var(--c-1);
	color: var(--c-f);
	text-align: center;
	padding: 8px 16px;
	border-radius: 6px;
	z-index: 1;
	pointer-events: none;
}

 .tooltip::after {
	content: "";
	position: absolute;
	border: 8px solid;
	border-color: var(--c-5) transparent transparent transparent;
	top: 100%;
	left: calc(50% - 8px);
	z-index: 0;
 }

.tooltip.visible {
	opacity: 1;
	visibility: visible;
}

.fade {
	visibility: hidden; /* hide it */
	opacity: 0; /* make it transparent */
	transform: scaleY(0); /* shrink content */
	height: 0; /* force other elements to move */
	padding: 0; /* remove empty space */
}

.first {
	margin-top: 10px;
}

#toast {
	opacity: 0;
	background-color: var(--c-5);
	border: 1px solid var(--c-2);
	max-width: 90%;
	color: var(--c-f);
	text-align: center;
	border-radius: 5px;
	padding: 22px;
	position: fixed;
	z-index: 5;
	left: 50%;
	transform: translateX(-50%);
	bottom: calc(var(--bh) + 22px);
	font-size: 17px;
	pointer-events: none;
}

#toast.show {
	opacity: 1;
	animation: fadein .5s, fadein .5s 2.5s reverse;
}

#toast.error {
	opacity: 1;
	background-color: #b21;
	animation: fadein .5s;
}

.modal {
	position: fixed;
	left: 0;
	bottom: 0;
	right: 0;
	top: calc(var(--th) - 1px);
	background-color: var(--c-o);
	transform: translateY(100%);
	transition: transform .4s;
	padding: 8px;
	font-size: 20px;
	overflow: auto;
}

.close {
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	float: right;
}

#info, #nodes {
	z-index: 4;
}

#rover {
	z-index: 3;
}

#rover .ibtn {
	margin: 5px;
}

#ndlt {
	margin: 12px 0;
}

#roverstar {
	position: fixed;
	top: calc(var(--th) + 5px);
	left: 1px;
	cursor: pointer;
}

#connind {
	position: fixed;
	bottom: calc(var(--bh) + 5px);
	left: 4px;
	padding: 5px;
	border-radius: 5px;
	background-color: #a90;
	z-index: -2;
}

#info .slider {
	max-width: 200px;
	min-width: 145px;
	float: right;
	margin: 0;
}
#info .sliderwrap {
	width: 200px;
}

#info table, #nodes table {
	table-layout: fixed;
	width: 100%;
}

#info td, #nodes td {
  padding-bottom: 8px;
}

#info .ibtn {
	margin: 5px;
}

#info div, #nodes div {
	max-width: 490px;
	margin: 0 auto;
}

#info #imgw {
	margin: 8px auto;
}

#lv {
	max-width: 600px;
	display: inline-block;
}

#heart {
	transition: color .9s;
	font-size: 16px;
	color: #f00;
}

img {
	max-width: 100%;
	max-height: 100%;
}

.wi {
	image-rendering: pixelated;
	image-rendering: crisp-edges;
	width: 210px;
}

@keyframes fadein {
	from {bottom: 0; opacity: 0;}
	to {bottom: calc(var(--bh) + 22px); opacity: 1;}
}

.sliderdisplay {
	content:'';
	position: absolute;
	top: 12px; left: 8px; right: 8px;
	height: 5px;
	background: var(--c-4);
	border-radius: 16px;
	pointer-events: none;
	z-index: -1;
	--bg: var(--c-f);
}

#rwrap .sliderdisplay { --bg: none; background: linear-gradient(90deg, #000 -15%, #f00); } /* -15% since #000 is too dark */
#gwrap .sliderdisplay { --bg: none; background: linear-gradient(90deg, #000 -15%, #0f0); } /* -15% since #000 is too dark */
#bwrap .sliderdisplay { --bg: none; background: linear-gradient(90deg, #000 -15%, #00f); } /* -15% since #000 is too dark */
#wwrap .sliderdisplay { --bg: none; background: linear-gradient(90deg, #000 -15%, #fff); } /* -15% since #000 is too dark */
#kwrap .sliderdisplay,
#wbal  .sliderdisplay { background: linear-gradient(90deg, #ff8f1f 0%, #fff 50%, #cbdbff); }

/* wrapper divs hidden by default */
#liveview, #liveview2D, #roverstar, #pql
#rgbwrap, #swrap, #hwrap, #kwrap, #wwrap, #wbal, #qcs-w, #hexw,
.clear-icon, .edit-icon, .ptxt {
	display: none;
}

.sliderbubble {
	width: 24px;
	position: absolute;
	display: inline-block;
	border-radius: 16px;
	background: var(--c-3);
	color: var(--c-f);
	padding: 4px;
	font-size: 14px;
	right: 6px;
	transition: visibility .25s ease,opacity .25s ease;
	opacity: 0;
	visibility: hidden;
	/* left: 8px; */
	top: 4px;
}

output.sliderbubbleshow {
	visibility: visible;
	opacity: 1;
}

input[type=range] {
	-webkit-appearance: none;
	width: 100%;
	padding: 0;
	margin: 0;
	background-color: transparent;
	cursor: pointer;
}

input[type=range]:focus {
	outline: 0;
}
input[type=range]::-webkit-slider-runnable-track {
	width: 100%;
	height: 30px;
	cursor: pointer;
	background: transparent;
}
input[type=range]::-webkit-slider-thumb {
	height: 16px;
	width: 16px;
	border-radius: 50%;
	background: var(--c-f);
	cursor: pointer;
	-webkit-appearance: none;
	margin-top: 7px;
}
input[type=range]::-moz-range-track {
	width: 100%;
	height: 30px;
	background-color: rgba(0, 0, 0, 0);
}
input[type=range]::-moz-range-thumb {
	border: 0 solid rgba(0, 0, 0, 0);
	height: 16px;
	width: 16px;
	border-radius: 50%;
	background: var(--c-f);
	transform: translateY(5px);
}
#Colors input[type=range]::-webkit-slider-thumb {
	height: 18px;
	width: 18px;
	border: 2px solid var(--c-1);
	margin-top: 5px;
}
#Colors input[type=range]::-moz-range-thumb {
	border: 2px solid var(--c-1);
}

#Colors .sliderwrap {
	margin: 2px 0 0;
}

/* Dynamically hide labels */
.hd {
	display: var(--bhd);
}
/* Do not hide quick load label in simplified mode on small screen widths */
.simplified #pql .hd {
	display: var(--bhd) !important;
}

#briwrap {
	min-width: 300px;
	float: right;
	margin-top: var(--bmt);
}

#picker {
	margin: 4px auto 0 !important;
	max-width: max-content;
}

/* buttons */
.btn {
	padding: 8px;
	/*margin: 10px 4px;*/
	width: 230px;
	font-size: 19px;
	color: var(--c-d);
	cursor: pointer;
	border-radius: 25px;
	transition-duration: .3s;
	-webkit-backface-visibility: hidden;
	-webkit-transform: translate3d(0,0,0);
	backface-visibility: hidden;
	transform: translate3d(0,0,0);
	overflow: hidden;
	text-overflow: ellipsis;
	border: 1px solid var(--c-3);
	background-color: var(--c-3);
}
#segutil .btn-s:hover,
#segutil2 .btn-s:hover,
#putil .btn-s:hover,
.btn:hover {
	border: 1px solid var(--c-5) /*!important*/;
	background-color: var(--c-5) /*!important*/;
}
.btn-s {
	width: 100%;
	margin: 0;
}
.btn-icon {
	margin: -4px 4px -1px 0;
	vertical-align: middle;
	display: inline-block;
}
.btn-n {
	width: 230px;
	margin: 0 8px 0 0;
}
.btn-p {
	width: 120px;
	margin: 5px 0;
}
.btn-xs, .btn-pl-del, .btn-pl-add {
	width: 42px !important;
	height: 42px !important;
	text-overflow: clip;
}
.btn-xs {
	margin: 0;
}
#info .btn-xs {
	border: 1px solid var(--c-4);
}
#btns .btn-xs {
	margin: 0 4px;
}

#putil .btn-s {
	width: 135px;
}

#nodes .ibtn {
	margin: 0;
}

#segutil .btn-s, #segutil2 .btn-s, #putil .btn-s {
	background-color: var(--c-3);
	border: 1px solid var(--c-3);
}

.btn-pl-del, .btn-pl-add {
	margin: 0;
	white-space: nowrap;
}
a.btn {
	display: block;
	white-space: nowrap;
	text-align: center;
	padding: 9px 32px 7px 24px;
	position: relative;
	box-sizing: border-box;
	line-height: 24px;
}

/* Quick color select wrapper div */
#qcs-w {
	margin-top: 10px;
}

/* Quick color select buttons */
.qcs {
	margin: 2px;
	border-radius: 14px;
	display: inline-block;
	width: 28px;
	height: 28px;
	line-height: 28px;
}

/* Quick color select Black and White button (has white/black border, depending on the theme) */
.qcsb, .qcsw {
	width: 26px;
	height: 26px;
	line-height: 26px;
	border: 1px solid var(--c-f);
}

/* Hex color input wrapper div */
#hexw {
	margin-top: 5px;
}

select {
	padding: 4px 8px;
	margin: 0;
	font-size: 19px;
	background-color: var(--c-3);
	color: var(--c-d);
	cursor: pointer;
	border: 0 solid var(--c-2);
	border-radius: 20px;
	transition-duration: .5s;
	-webkit-backface-visibility: hidden;
	-webkit-transform: translate3d(0,0,0);
	-webkit-appearance: none;
	-moz-appearance: none;
	backface-visibility: hidden;
	transform: translate3d(0,0,0);
	text-overflow: ellipsis;
}
#tt {
	text-align: center;
}
select.sel-p, select.sel-pl, select.sel-ple {
	margin: 5px 0;
	width: 100%;
	height: 40px;
	padding: 0 20px 0 8px;
}
div.sel-p {
	position: relative;
}
div.sel-p:after {
	content: "";
	position: absolute;
	right: 10px;
	top: 22px;
	width: 0;
	height: 0;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-top: 8px solid var(--c-f);
}
select.sel-ple {
	text-align: center;
}
select.sel-sg {
	margin: 5px 0;
	height: 40px;
}
option {
	background-color: var(--c-3);
	color: var(--c-f);
}
input[type=number],
input[type=text] {
	background: var(--c-3);
	color: var(--c-f);
	border: 0 solid var(--c-2);
	border-radius: 10px;
	padding: 8px;
	/*margin: 6px 6px 6px 0;*/
	font-size: 19px;
	transition: background-color .2s;
	outline: 0;
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
	appearance: textfield;
}

input[type=number] {
	text-align: right;
	width: 50px;
}

input[type=text] {
	text-align: center;
}

input[type=number]:focus,
input[type=text]:focus {
	background: var(--c-6);
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
	-webkit-appearance: none;
}

#hexw input[type=text] {
	width: 6em;
}

input[type=text].ptxt {
	width: calc(100% - 24px);
}

textarea {
	background: var(--c-2);
	color: var(--c-f);
	width: calc(100% - 14px); /* +padding=260px */
	height: 90px;
	border-radius: 5px;
	border: 2px solid var(--c-5);
	outline: 0;
	resize: none;
	font-size: 19px;
	padding: 5px;
}

.apitxt {
	height: 7em;
}

::selection {
	background: var(--c-b);
}

.ptxt {
  	margin: -1px 4px 8px !important;
}

.stxt {
	width: 50px !important;
}

.segname, .pname {
	white-space: nowrap;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	line-height: 24px;
	padding: 8px 24px;
	max-width: 170px;
	position: relative;
}

.segname .flr, .pname .flr {
	transform: rotate(0deg);
	/*right: -6px;*/
}

/* segment power wrapper */
.sbs {
	/*padding: 1px 0 1px 20px;*/
	display: var(--sgp);
	width: 100%;
	position: relative;
}

.pname {
	top: 1px;
}
.plname {
	top: 0;
}

/* preset id number */
.pid {
	position: absolute;
	top: 8px;
	left: 12px;
	font-size: 16px;
	text-align: center;
	color: var(--c-b);
}

.newseg {
	cursor: default;
}
/*
.ic {
	padding: 6px 0 0 0;
}
*/
/* color selector */
#csl button {
	width: 44px;
	height: 44px;
	margin: 5px;
	border: 2px solid var(--c-d) !important;
	background-color: #000;
}
/* selected color selector */
#csl .sl {
	margin: 2px;
	width: 50px;
	height: 50px;
	border-width: 5px !important;
}

.qcs, #namelabel { /* text shadow for name to be legible on grey backround */
	text-shadow: -1px -1px 0 var(--c-1), 1px -1px 0 var(--c-1), -1px 1px 0 var(--c-1), 1px 1px 0 var(--c-1);
}

.psts {
	color: var(--c-f);
	margin: 4px;
}

.pwr {
	color: var(--c-6);
	cursor: pointer;
}

.act {
	color: var(--c-f);
}

.del {
	position: absolute;
	bottom: 8px;
	right: 8px;
}

.frz {
	left: 10px;
	position: absolute;
	top: 8px;
	cursor: pointer;
	z-index: 1;
}

/* radiobuttons and checkmarks */
.check, .radio {
	display: block;
	position: relative;
	cursor: pointer;
}

.revchkl {
	padding: 4px 0 0 35px;
	margin-bottom: 0;
	margin-top: 8px;
}

TD .revchkl {
	padding: 0 0 0 32px;
	margin-top: 0;
}

.check input, .radio input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
	height: 0;
	width: 0;
}

.checkmark, .radiomark {
	position: absolute;
	height: 24px;
	width: 24px;
	top: 0;
	bottom: 0;
	left: 0;
	background-color: var(--c-3);
	border: 1px solid var(--c-2);
}

.radiomark {
	top: 8px;
	left: 8px;
	height: 22px;
	width: 22px;
	border-radius: 50%;
	background-color: transparent;
}

.checkmark {
	border-radius: 10px;
}

.radio:hover input ~ .radiomark,
.check:hover input ~ .checkmark {
	background-color: var(--c-5);
}

.checkmark:after, .radiomark:after {
	content: "";
	position: absolute;
	display: none;
}

.check .checkmark:after {
	left: 9px;
	top: 4px;
	width: 5px;
	height: 10px;
	border: solid var(--c-f);
	border-width: 0 3px 3px 0;
}

.rot45,
.check .checkmark:after {
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}

.radio .radiomark:after {
	width: 14px;
	height: 14px;
	top: 50%;
	left: 50%;
	margin: -7px;
	border-radius: 50%;
	background: var(--c-f);
}

TD .checkmark, TD .radiomark {
	top: -6px;
}

.h {
	font-size: 13px;
	text-align: center;
	color: var(--c-b);
}

.bp {
	margin-bottom: 8px;
}

/* segment & preset wrapper */
.seg, .pres {
	background-color: var(--c-2);
	/*color: var(--c-f);*/ /* seems to affect only the Add segment button, which should be same color as reset segments */
	border: 0 solid var(--c-f);
	text-align: left;
	transition: background-color .5s;
	border-radius: 21px;
}

.seg {
	top: auto !important;	/* prevent sticky */
	bottom: auto !important;
}
/* checkmark labels */
.seg .schkl {
	position: absolute;
	top: 7px;
	left: 9px;
}
/* checkmark labels */
.filter .fchkl, .option .ochkl {
	display: inline-block;
	min-width: .7em;
	padding: 1px 4px 1px 32px;
	text-align: left;
	line-height: 24px;
	vertical-align: middle;
	-webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	filter: grayscale(100%);
}
.filter .fchkl {
	margin: 0 4px;
	min-width: 20px;
	pointer-events: auto;
}

.lbl-l {
	font-size: 13px;
	text-align: center;
	padding: 4px 0;
}

.lbl-s {
	display: inline-block;
	margin-top: 6px;
	font-size: 13px;
	width: 48%;
	text-align: center;
}

/* list wrapper */
.list {
	position: relative;
	transition: background-color .5s;
	margin: auto auto 10px;
	line-height: 24px;
}

/* list item */
.lstI {
	align-items: center;
	cursor: pointer;
	background-color: var(--c-2);
	overflow: hidden;
	position: -webkit-sticky;
	position: sticky;
	border-radius: 21px;
	margin: 0 auto 12px;
	min-height: 40px;
	border: 1px solid var(--c-2);
	width: 100%;
}

#segutil {
	margin-bottom: 12px;
}

#segcont > div:first-child, #fxFind  {
	margin-top: 4px;
}

/* Simplify segments */
.simplified #segcont .lstI {
	margin-top: 4px;
	min-height: unset;
}

/* selected item/element */
.selected { /* has to be after .lstI since !important is not ok */
	background: var(--c-4);
}

#segcont .seg:hover:not([class*="expanded"]),
.lstI:hover:not([class*="expanded"]) {
	background: var(--c-5);
}

.selected .checkmark,
.selected .radiomark,
.selected input[type=number],
.selected input[type=text] {
	background-color: var(--c-3);
}

/* selected list item */
.lstI.selected {
	top: 0;
	bottom: 0;
	border: 1px solid var(--c-4);
}

.lstI.sticky,
.lstI.selected {
	z-index: 1;
	box-shadow: 0 0 10px 4px var(--c-1);
}

.lstI .flr:hover {
    background: var(--c-6);
    border-radius: 100%;
}

#pcont .selected:not([class*="expanded"]) {
	bottom: 52px;
	top: 42px;
}

#fxlist .lstI.selected {
	top: calc(var(--sti) + 42px);
}
#pallist .lstI.selected {
	top: calc(var(--stp) + 42px);
}

dialog::backdrop {
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}
dialog {
	max-height: 70%;
	border: 0;
	border-radius: 10px;
	background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.1)), var(--c-3);
	box-shadow: 4px 4px 10px 4px var(--c-1);
	color: var(--c-f);
}

#fxlist .lstI.sticky {
	top: var(--sti);
}
#pallist .lstI.sticky {
	top: var(--stp);
}

/* list item content */
.lstIcontent {
	padding: 9px 0 7px;
	position: relative;
}

/* list item name (for sorting) */
.lstIname {
	white-space: nowrap;
	text-overflow: ellipsis;
	-webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	filter: grayscale(100%);
}

/* list item palette preview */
.lstIprev {
	width: 100%;
	height: 6px;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: -1;
}

/* find/search element */
.fnd {
	position: relative;
}

.fnd input[type="text"] {
	display: block;
	width: 100%;
	box-sizing: border-box;
	padding: 8px 40px 8px 44px;
	margin: 4px auto 12px;
	text-align: left;
	border-radius: 21px;
	background: var(--c-2);
	border: 1px solid var(--c-3);
	-webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	filter: grayscale(100%);
}

.fnd input[type="text"]:focus {
	background-color: var(--c-4);
}

.fnd input[type="text"]:not(:placeholder-shown),
.fnd input[type="text"]:hover {
	background-color: var(--c-3);
}

#fxFind.fnd input[type="text"] {
	margin-bottom: 0;
}
#fxFind {
	margin-bottom: 12px;
}

/* segment & preset inner/expanded content */
.segin,
.presin {
	padding: 8px;
	position: relative;
}

.presin {
	width: 100%; 
	box-sizing: border-box;
}

.btn-s,
.btn-n {
	border: 1px solid var(--c-2);
	background-color: var(--c-2);
}
.modal .btn:hover,
.segin .btn:hover {
	border: 1px solid var(--c-5) /*!important*/;
	background-color: var(--c-5) /*!important*/;
}

/* hidden list items, must be after .expanded */
.pres .lstIcontent, .segin {
	display: none;
}

.check input:checked ~ .checkmark:after,
.radio input:checked ~ .radiomark:after,
.show,
.expanded .edit-icon,
.expanded .segin, .expanded .presin, .expanded .sbs,
.expanded {
	display: inline-block !important;
}
.hide, .expanded .segin.hide, .expanded .presin.hide, .expanded .sbs.hide, .expanded .frz, .expanded .g-icon {
	display: none !important;
}

.m6 {
	margin: 6px 0;
}

.c {
	text-align: center;
}

.po2 {
	display: none;
	margin-top: 8px;
}

.pwarn {
	color: red;
}

/* horizontal divider (playlist entries) */
.hrz {
	width: auto;
	height: 2px;
	background-color: var(--c-b);
	margin: 3px 0;
}

::-webkit-scrollbar {
	width: 6px;
}
::-webkit-scrollbar-track {
	background: transparent;
}
::-webkit-scrollbar-thumb {
	background: var(--c-sb);
	opacity: .2;
	border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
	background: var(--c-sbh);
}

@media not all and (hover: none) {
	.sliderwrap:hover + output.sliderbubble {
		visibility: visible;
		opacity: 1;
	}
}

@media all and (max-width: 1023px) {
	.top button {
		width: 8%;
		padding: 10px 0 8px 0;
	}
	#buttonPcm {
		display: none;
	}
}

@media all and (max-width: 335px) {
	.sliderbubble {
		display: none;
  	}
}

@media all and (max-width: 550px) and (min-width: 374px) {
	#info table .btn, #nodes table .btn {
		width: 200px;
	}
	#info .ibtn, #nodes .ibtn {
		width: 145px;
	}
	#info div, #nodes div, #nodes a.btn {
		max-width: 320px;
	}
}

@media all and (max-width: 420px) {
	#buttonNodes {
		display: none;
	}
}

@media all and (max-width: 639px) {
	.top button {
		width: 16.6%;
		padding: 8px 0 4px 0;
	}
	#briwrap {
		margin: 0 auto !important;
		float: none;
		display: inline-block;
	}
	.hd {
		display: none !important;
	}
}

@media all and (min-width: 420px) and (max-width: 639px) {
	.top button {
		width: 14.28%;
		padding: 8px 0 4px 0;
	}
}

@media all and (min-width: 640px) and (max-width: 767px) {
	#buttonNodes {
		display: none;
	}
}

/* small screen & tablet "PC mode" support */
@media all and (min-width: 1024px) and (max-width: 1249px) {
	#segutil, #segutil2, #segcont, #putil, #pcont, #pql, #fx, #palw, #psFind, #sliders {
		width: 100%;
		max-width: 280px;
		font-size: 18px;
	}
	#putil .btn-s {
		width: 114px;
	}
	#sliders .sliderbubble {
		display: none;
	}
	#sliders .sliderwrap, .sbs .sliderwrap {
		width: calc(100% - 42px);
	}
	#sliders .slider {
		padding-right: 0;
	}
	#sliders .sliderwrap {
		left: 12px;
	}
	.segname {
		max-width: calc(100% - 110px);
	}
	.segt TD {
		padding: 0 !important;
	}
	input[type="number"], input[type=text], select, textarea {
		font-size: 18px;
	}
	input[type="number"] {
		width: 32px;
	}
	.lstIcontent {
		padding-left: 8px;
	}
	.revchkl {
		max-width: 183px;
		text-overflow: ellipsis;
		overflow-x: clip;
	}
}
