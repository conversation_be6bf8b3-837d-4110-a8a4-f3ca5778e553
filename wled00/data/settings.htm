<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
	<title>WLED Settings</title>
	<script src="common.js" async type="text/javascript"></script>
	<script>
		function S() {
			getLoc();
			loadJS(getURL('/settings/s.js?p=0'), false);	// If we set async false, file is loaded and executed, then next statement is processed
		}
	</script>
	<style>
		body {
			text-align: center;
			background: #222;
			height: 100px;
			margin: 0;
		}
		html {
			--h: 9vh;
		}
		button {
			background: #333;
			color: #fff;
			font-family: Verdana, Helvetica, sans-serif;
			display: block;
			border: 1px solid #333;
			border-radius: var(--h);
			font-size: 6vmin;
			height: var(--h);
			width: calc(100% - 40px);
			margin: 2vh auto 0;
			cursor: pointer;
		}
	</style>
</head>
<body onload="S()">
<button type=submit id="b" onclick="window.location=getURL('/')">Back</button>
<button type="submit" onclick="window.location=getURL('/settings/wifi')">WiFi Setup</button>
<button type="submit" onclick="window.location=getURL('/settings/leds')">LED Preferences</button>
<button id="2dbtn" type="submit" onclick="window.location=getURL('/settings/2D')">2D Configuration</button>
<button type="submit" onclick="window.location=getURL('/settings/ui')">User Interface</button>
<button id="dmxbtn" style="display:none;" type="submit" onclick="window.location=getURL('/settings/dmx')">DMX Output</button>
<button type="submit" onclick="window.location=getURL('/settings/sync')">Sync Interfaces</button>
<button type="submit" onclick="window.location=getURL('/settings/time')">Time & Macros</button>
<button type="submit" onclick="window.location=getURL('/settings/um')">Usermods</button>
<button type="submit" onclick="window.location=getURL('/settings/sec')">Security & Updates</button>
</body>
</html>