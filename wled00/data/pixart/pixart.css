
.box {
  border: 2px solid #fff;
}
body {
  font-family: Arial, sans-serif;
  background-color: #111;
}

.top-part {
  width: 600px;
  margin: 0 auto;
}
.container {
  max-width: 100% -40px;
  border-radius: 0px;
  padding: 20px;
  text-align: center;
}
h1 {
  font-size: 2.3em;
  color: #ddd;
  margin: 1px 0;
  font-family: Arial, sans-serif;
  line-height: 0.5;
  /*text-align: center;*/
}
h2 {
  font-size: 1.1em;
  color: rgba(221, 221, 221, 0.61);
  margin: 1px 0;
  font-family: Arial, sans-serif;
  line-height: 0.5;
  text-align: center;
}
h3 {
  font-size: 0.7em;
  color: rgba(221, 221, 221, 0.61);
  margin: 1px 0;
  font-family: Arial, sans-serif;
  line-height: 1.4;
  text-align: center;
  align-items: center;
  justify-content: center;
  display: flex;
}

p {
  font-size: 1em;
  color: #777;
  line-height: 1.5;
  font-family: Arial, sans-serif;
}

#fieldTable {
  font-size: 1  em;
  color: #777;
  line-height: 1;
  font-family: Arial, sans-serif;
}

#scaleTable {
  font-size: 1  em;
  color: #777;
  line-height: 1;
  font-family: Arial, sans-serif;
}

#drop-zone {
  display: block;
  width: 100%-40px;
  border: 3px dashed #ddd;
  border-radius: 0px;
  text-align: center;
  padding: 20px;
  margin: 0px;
  cursor: pointer;
  font-family: Arial, sans-serif;
  font-size: 15px;
  color: #777;
}

#file-picker {
  display: none;
}
.adaptiveTD{
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;

}

.mainSelector {
  background-color: #222;
  color: #ddd;
  border: 1px solid #333;
  margin-top: 4px;
  margin-bottom: 4px;
  padding: 0 8px;
  height: 28px;
  font-size: 15px;
  border-radius: 7px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.adaptiveSelector {
  background-color: #222;
  color: #ddd;
  border: 1px solid #333;
  margin-top: 4px;
  margin-bottom: 4px;
  padding: 0 8px;
  height: 28px;
  font-size: 15px;
  border-radius: 7px;
  flex-grow: 1;
  display: none;
}

.segmentsDiv{
  width: 36px;
  padding-left: 5px;
}

* input[type=range] {
	appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
	flex-grow: 1;
	padding: 0;
	margin: 4px 8px 4px 0;
	background-color: transparent;
	cursor: pointer;
  background: linear-gradient(to right, #bbb 50%, #333 50%);
  border-radius: 7px;
}

input[type=range]:focus {
	outline: none;
}
input[type=range]::-webkit-slider-runnable-track {
	height: 28px;
	cursor: pointer;
	background: transparent;
  border-radius: 7px;
}
input[type=range]::-webkit-slider-thumb {
	height: 16px;
	width: 16px;
	border-radius: 50%;
	background: #fff;
	cursor: pointer;
	-webkit-appearance: none;
	margin-top: 4px;
  border-radius: 7px;
}
input[type=range]::-moz-range-track {
	height: 28px;
	background-color: rgba(0, 0, 0, 0);
  border-radius: 7px;
}
input[type=range]::-moz-range-thumb {
	border: 0px solid rgba(0, 0, 0, 0);
	height: 16px;
	width: 16px;
	border-radius: 7px;
	background: #fff;
}

.rangeNumber{
  width: 20px;
  vertical-align: middle;
}

.fullTextField[type=text] {
  background-color: #222;
  border: 1px solid #333;
  padding-inline-start: 5px;
  margin-top: 4px;
  margin-bottom: 4px;
  height: 24px;
  border-radius: 0px;
  font-family: Arial, sans-serif;
  font-size: 15px;
  color: #ddd;
  border-radius: 7px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.flxTFld{
  background-color: #222;
  border: 1px solid #333;
  padding-inline-start: 5px;
  height: 24px;
  border-radius: 0px;
  font-family: Arial, sans-serif;
  font-size: 15px;
  color: #ddd;
  border-radius: 7px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

* input[type=submit] {
  background-color: #222;
  border: 1px solid #333;
  padding: 0.5em;
  width: 100%;
  border-radius: 24px;
  font-family: Arial, sans-serif;
  font-size: 1.3em;
  color: #ddd;
}

* button {
  background-color: #222;
  border: 1px solid #333;
  padding-inline: 5px;
  width: 100%;
  border-radius: 24px;
  font-family: Arial, sans-serif;
  font-size: 1em;
  color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

#scaleDiv {
  display: flex;
  align-items: center;
  vertical-align: middle;
}

textarea {
  grid-row: 1 / 2;
  width: 100%;
  height: 200px;
  background-color: #222;
  border: 1px solid #333;
  color: #ddd;
}
.hide {
    display: none;
}

.svg-icon {
  vertical-align: middle;
}
#image-container {
  display: grid;
  grid-template-rows: 1fr 1fr;
}
#button-container {
  display: flex;
  padding-bottom: 10px;
  padding-top: 10px;
}

.buttonclass {
  flex: 1;
  padding-top: 5px;
  padding-bottom: 5px;
}

.gap {
  width: 10px;
}

#submitConvert::before {
  content: "";
  display: inline-block;
  background-image: url('data:image/svg+xml;utf8, <svg style="width:24px;height:24px" viewBox="0 0 24 24" <path fill="currentColor" d="M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z" /></svg>');
  width: 36px;
  height: 36px;
}

#sizeDiv * {
  display: inline-block;
}
.sizeInputFields{
  width: 50px;
  background-color: #222;
  border: 1px solid #333;
  padding-inline-start: 5px;
  margin-top: -5px;
  height: 24px;
  border-radius: 7px;
  font-family: Arial, sans-serif;
  font-size: 15px;
  color: #ddd;
}
a:link {
  color: rgba(221, 221, 221, 0.61);
  background-color: transparent;
  text-decoration: none;
}

a:visited {
  color: rgba(221, 221, 221, 0.61);
  background-color: transparent;
  text-decoration: none;
}

a:hover {
  color: #ddd;
  background-color: transparent;
  text-decoration: none;
}

a:active {
  color: rgba(221, 221, 221, 0.61);
  background-color: transparent;
  text-decoration: none;
}