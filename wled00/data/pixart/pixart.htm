<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>WLED Pixel Art Converter</title>
    <link rel="stylesheet" href="pixart.css">
    <link rel="shortcut icon" href="favicon-16x16.png">
    <script type="text/javascript">
      var d = document;
      function gId(e) {return d.getElementById(e);}
      function cE(e) {return d.createElement(e);}
    </script>
  </head>
  <body>
    <body>
      <div class="top-part" >
        <div style="display: flex; justify-content: center;">
          <h1 style="display: flex; align-items: center;">
            <svg style="width:36px;height:36px;margin-right:6px;" viewBox="0 0 32 32">
              <rect style="fill:#003FFF" x="6" y="22" width="8" height="4"/>
              <rect style="fill:#003FFF" x="14" y="14" width="4" height="8"/>
              <rect style="fill:#003FFF" x="18" y="10" width="4" height="8"/>
              <rect style="fill:#003FFF" x="22" y="6" width="8" height="4"/>
            </svg>
            WLED Pixel Art Converter
          </h1>
        </div>
        <h2>Convert image to WLED JSON (pixel art on WLED matrix)</h2>
        <p>
          <table id="fieldTable"  style="width: 100%; table-layout: fixed; align-content: center;">
            <tr>
              <td style="vertical-align: middle;">
                <label for="ledSetupSelector">Led setup:</label>
              </td>
              <td class="adaptiveTD">
                <select id="ledSetupSelector" class="mainSelector">
                  <option value="matrix" selected>2D Matrix</option>
                  <option value="r2l">Serpentine, first row right to left &lt;-</option>
                  <option value="l2r">Serpentine, first row left to right -&gt;</option>
                </select>
              </td>
            </tr>        
            <tr>
              <td style="vertical-align: middle;">
                <label for="formatSelector">Output format:</label>
              </td>
              <td class="adaptiveTD">
                <select id="formatSelector" class="mainSelector">
                  <option value="wled" selected>WLED JSON</option>
                  <option value="curl">CURL</option>
                  <option value="ha">Home Assistant YAML</option>
                </select>
              </td>
            </tr>        
            <tr>
              <td style="vertical-align: middle;">
                <label for="colorFormatSelector">Color code format:</label>
              </td>
              <td class="adaptiveTD">
                <select id="colorFormatSelector" class="mainSelector">
                  <option value="hex" selected>HEX (&quot;f4f4f4&quot;)</option>
                  <option value="dec">DEC (244,244,244)</option>
                </select>
            </td>
            </tr>
            <tr>
              <td style="vertical-align: middle;">
                <label for="addressingSelector">Addressing:</label>
              </td>
              <td class="adaptiveTD">
                <select id="addressingSelector" class="mainSelector">
                  <option value="hybrid" selected>Hybrid (&quot;f0f0f0&quot;,10, 17, &quot;f4f4f4&quot;)</option>
                  <option value="range">Range (10, 17, &quot;f4f4f4&quot;)</option>
                  <option value="single">Single (&quot;f4f4f4&quot;)</option>
                </select>
              </td>
            </tr>
            <tr>
              <td style="vertical-align: middle;">
                <label for="brightnessNumber">Brightness:</label>
              </td>
              <td style="vertical-align: middle; display: flex; align-items: center;">
                <input type="range" id="brightnessNumber" min="1" max="255" value="128">
                <span id="brightnessValue">128</span>
              </td>
            </tr>
            <tr>
              <td style="vertical-align: middle;">
                <label for="colorLimitNumber">Max no of colors/JSON:</label>
              </td>
              <td style="vertical-align: middle; display: flex; align-items: center;">
                <input type="range" id="colorLimitNumber" min="1" max="512" value="256">
                <span id="colorLimitValue" >256</span>
              </td>
            </tr>
            <tr class="ha-hide">
              <td style="vertical-align: middle;">
                <label for="haID">HA Device ID:</label>
              </td>
              <td class="adaptiveTD">
                <input class="fullTextField" type="text" id="haID" value="pixel_art_controller_001">
              </td>
            </tr>
            <tr class="ha-hide">
              <td style="vertical-align: middle;">
                <label for="haUID">HA Device Unique ID:</label>
              </td>
              <td class="adaptiveTD">
                <input class="fullTextField" type="text" id="haUID" value="pixel_art_controller_001a">
              </td>
            </tr>
            <tr class="ha-hide">
              <td style="vertical-align: middle;">
                <label for="haName">HA Device Name:</label>
              </td>
              <td class="adaptiveTD">
                <input class="fullTextField" type="text" id="haName" value="Pixel Art Kitchen">
              </td>
            </tr>
            <tr>
              <td style="vertical-align: middle;">
                <label for="curlUrl">Device IP/host name:</label>
              </td>
              <td class="adaptiveTD">
                <input class="fullTextField" type="text" id="curlUrl" value="">
              </td>
            </tr>
            <tr>
              <td style="vertical-align: middle;">
                <label for="targetSegment">Target segment id:</label>
              </td>
              <td class="adaptiveTD">
                <input class="flxTFld" type="number" id="segID" value="0" min="0" max="63">
                <select id="targetSegment" class="adaptiveSelector">
                </select>
                <div id="getSegmentsDiv" class="segmentsDiv"></div>
              </td>
            </tr>
          </table>
          <table  class= "scaleTableClass" id="scaleTable"  style="width: 100%; table-layout: fixed; align-content: center;">
            <tr>
              <td style="vertical-align: middle;">
                <div id="scaleDiv">
                  <svg id="scaleToggle" style="width:36px;height:36px; cursor: pointer;" viewBox="0 0 24 24" onclick="switchScale()">
                    <path id="scaleTogglePath" fill="currentColor" d="M17,7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7M7,15A3,3 0 0,1 4,12A3,3 0 0,1 7,9A3,3 0 0,1 10,12A3,3 0 0,1 7,15Z" />
                  </svg>
                   &nbsp;Scale image
                </div>
              </td>
              <td style="vertical-align: middle;">
                <div id="sizeDiv" style="display: none;">
                  <label for="sizeX">W : </label> &nbsp;<input class="sizeInputFields" type="number" id="sizeX" min="1" value="16">
                  &nbsp;&nbsp;&nbsp;
                  <label for="sizeY">H : </label> &nbsp;<input class="sizeInputFields" type="number" id="sizeY" min="1" value="16">
                </div>
              </td>
            </tr>
          </table>
        </p>
  
        <p>
          <label for="file-picker">
            <div id="drop-zone">    
                Drop image here <br>or <br>
                Click to select a file
            </div>
          </label>
        </p>
        
        <p>
          <input type="file" id="file-picker" style="display: none;">
          <div style="width: 100%; text-align: center;">
            <img id="preview" style="display: none; margin: 0 auto;">
          </div>
          <!--
          <div id="submitConvertDiv" style="display: none;">
            <button id="convertbutton" class="buttonclass"></button>
          </div>
          -->
          <div id="raw-image-container" style="display: none">
            <img id="image" src="" alt="RawImage image">
          </div>
          
        </p>
        
        <div id="image-container" style="display: none;">
          <div id="image-info" style="display: none"></div>
          <textarea id="JSONled" readonly></textarea>
        </div>
  
        <div id="button-container" style="display: none;">
          <button id="copyJSONledbutton" class="buttonclass"></button>
          <div id="gap1" class="gap"></div>
          <button id="sendJSONledbutton" class="buttonclass"></button>
        </div>
        <div>
          <h3><div id="version">Version 1.0.8</div>&nbsp;-&nbsp; <a href="https://github.com/werkstrom/WLED-PixelArtConverter/blob/main/README.md" target="_blank">Help/About</a></h3>
        </div>
      </div>
      <div id=bottom-part style="display: none" class=bottom-part></div>
            <canvas id="pixelCanvas"></canvas>
      </div>
      <script src="statics.js" type="text/javascript"></script>
      <script src="getPixelValues.js" type="text/javascript"></script>
      <script src="boxdraw.js" type="text/javascript"></script>
      <script src="pixart.js" type="text/javascript"></script>
  </body>
</html>