@font-face {
  font-family: 'wled122';
  src:
    url('fonts/wled122.woff2?e3eban') format('woff2'),
    url('fonts/wled122.ttf?e3eban') format('truetype'),
    url('fonts/wled122.woff?e3eban') format('woff'),
    url('fonts/wled122.svg?e3eban#wled122') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="i-"], [class*=" i-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'wled122' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.i-pattern:before {
  content: "\e23d";
}
.i-segments:before {
  content: "\e34b";
}
.i-sun:before {
  content: "\e333";
}
.i-palette:before {
  content: "\e2b3";
}
.i-eye:before {
  content: "\e0e8";
}
.i-speed:before {
  content: "\e325";
}
.i-expand:before {
  content: "\e395";
}
.i-power:before {
  content: "\e08f";
}
.i-settings:before {
  content: "\e0a2";
}
.i-playlist:before {
  content: "\e139";
}
.i-night:before {
  content: "\e2a2";
}
.i-cancel:before {
  content: "\e38f";
}
.i-sync:before {
  content: "\e116";
}
.i-confirm:before {
  content: "\e390";
}
.i-brightness:before {
  content: "\e2a6";
}
.i-nodes:before {
  content: "\e22d";
}
.i-add:before {
  content: "\e18a";
}
.i-edit:before {
  content: "\e2c6";
}
.i-intensity:before {
  content: "\e409";
}
.i-star:before {
  content: "\e410";
}
.i-info:before {
  content: "\e066";
}
.i-del:before {
  content: "\e037";
}
.i-presets:before {
  content: "\e04c";
}
