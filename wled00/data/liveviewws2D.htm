<!DOCTYPE html>
<html>
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
	<meta charset="utf-8">
	<meta name="theme-color" content="#222222">
	<title>WLED Live Preview</title>
	<style>
	body {
		margin: 0;
	}
	</style>
</head>
<body>
	<canvas id="canv"></canvas>
	<script>
		var c = document.getElementById('canv');
		var leds = "";
		var throttled = false;
		function setCanvas() {
			c.width  = window.innerWidth * 0.98; //remove scroll bars
			c.height = window.innerHeight * 0.98; //remove scroll bars
		}
		setCanvas();
		// Check for canvas support
		var ctx = c.getContext('2d');
		if (ctx) { // Access the rendering context
			// use parent WS or open new
			var ws;
			try {
				ws = top.window.ws;
			} catch (e) {}
			if (ws && ws.readyState === WebSocket.OPEN) {
				ws.send("{'lv':true}");
			} else {
				let l = window.location;
				let pathn = l.pathname;
				let paths = pathn.slice(1,pathn.endsWith('/')?-1:undefined).split("/");
				let url = l.origin.replace("http","ws");
				if (paths.length > 1) {
					url +=  "/" + paths[0];
				}
				ws = new WebSocket(url+"/ws");
				ws.onopen = ()=>{
					ws.send("{'lv':true}");
				}
			}
			ws.binaryType = "arraybuffer";
			ws.addEventListener('message',(e)=>{
				try {
					if (toString.call(e.data) === '[object ArrayBuffer]') {
						let leds = new Uint8Array(event.data);
						if (leds[0] != 76 || leds[1] != 2 || !ctx) return; //'L', set in ws.cpp
						let mW = leds[2]; // matrix width
						let mH = leds[3]; // matrix height
						let pPL = Math.min(c.width / mW, c.height / mH); // pixels per LED (width of circle)
						let lOf = Math.floor((c.width - pPL*mW)/2); //left offset (to center matrix)
						var i = 4;
						for (y=0.5;y<mH;y++) for (x=0.5; x<mW; x++) {
							ctx.fillStyle = `rgb(${leds[i]},${leds[i+1]},${leds[i+2]})`;
							ctx.beginPath();
							ctx.arc(x*pPL+lOf, y*pPL, pPL*0.4, 0, 2 * Math.PI);
							ctx.fill();
							i+=3;
						}
					}
				} catch (err) {
					console.error("Peek WS error:",err);
				} 
			});
		}
		// window.resize event listener
		window.addEventListener('resize', (e)=>{
			if (!throttled) {     // only run if we're not throttled
				setCanvas();      // actual callback action
				throttled = true; // we're throttled!
				setTimeout(()=>{  // set a timeout to un-throttle
					throttled = false;
				}, 250);
			}
		});
	</script>
</body>
</html>