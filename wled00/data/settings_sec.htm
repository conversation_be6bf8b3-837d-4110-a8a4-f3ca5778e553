<!DOCTYPE html>
<html lang="en">
<head>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
	<meta charset="utf-8">
	<title>Misc Settings</title>
	<script src="common.js" async type="text/javascript"></script>
	<script>
		function U() { window.open(getURL("/update"),"_self"); }
		function checkNum(o) {
			const specialkeys = ["Backspace", "Tab", "Enter", "Shift", "Control", "Alt", "Pause", "CapsLock", "Escape", "Space", "PageUp", "PageDown", "End", "Home", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "Insert", "Delete"];
			// true if key is a number or a special key
			if(event.key.match(/[0-9]/) || specialkeys.includes(event.key)) return true;
			event.preventDefault();
			return false;
		}
		function setBckFilename(x) {
			x.setAttribute("download","wled_" + x.getAttribute("download") + (sd=="WLED"?"":("_" +sd)));
		}
		function S() {
			getLoc();
			if (loc) {
				gId("bckcfg").setAttribute('href',getURL(gId("bckcfg").pathname));
				gId("bckpresets").setAttribute('href',getURL(gId("bckpresets").pathname));
			}
			loadJS(getURL('/settings/s.js?p=6'), false, undefined, ()=>{
				setBckFilename(gId("bckcfg"));
				setBckFilename(gId("bckpresets"));
			});	// If we set async false, file is loaded and executed, then next statement is processed
			if (loc) d.Sf.action = getURL('/settings/sec');
		}
	</script>
	<style>
		@import url("style.css");
	</style>
</head>
<body onload="S()">
	<form id="form_s" name="Sf" method="post">
		<div class="toprow">
		<div class="helpB"><button type="button" onclick="H('features/settings/#security-settings')">?</button></div>
		<button type="button" onclick="B()">Back</button><button type="submit">Save</button><hr>
		</div>
		<h2>Security & Update setup</h2>
		Settings PIN: <input type="password" id="PIN" name="PIN" size="4" maxlength="4" minlength="4" onkeydown="checkNum(this)" pattern="[0-9]*" inputmode="numeric" title="Please enter a 4 digit number"><br>
		<div class="warn">&#9888; Unencrypted transmission. Be prudent when selecting PIN, do NOT use your banking, door, SIM, etc. pin!</div><br>
		Lock wireless (OTA) software update: <input type="checkbox" name="NO"><br>
		Passphrase: <input type="password" name="OP" maxlength="32"><br>
		To enable OTA, for security reasons you need to also enter the correct password!<br>
		The password should be changed when OTA is enabled.<br>
		<b>Disable OTA when not in use, otherwise an attacker can reflash device software!</b><br>
		<i>Settings on this page are only changable if OTA lock is disabled!</i><br>
		Deny access to WiFi settings if locked: <input type="checkbox" name="OW"><br><br>
		Factory reset: <input type="checkbox" name="RS"><br>
		All settings and presets will be erased.<br><br>
		<div class="warn">&#9888; Unencrypted transmission. An attacker on the same network can intercept form data!</div>
		<span id="OTA"><hr>
		<h3>Software Update</h3>
		<button type="button" onclick="U()">Manual OTA Update</button><br>
		<div id="aOTA">Enable ArduinoOTA: <input type="checkbox" name="AO"></div>
		Only allow update from same network/WiFi: <input type="checkbox" name="SU"><br>
		<i class="warn">&#9888; If you are using multiple VLANs (i.e. IoT or guest network) either set PIN or disable this option.<br>
			Disabling this option will make your device less secure.</i><br></span>
		<hr id="backup">
		<h3>Backup & Restore</h3>
		<div class="warn">&#9888; Restoring presets/configuration will OVERWRITE your current presets/configuration.<br>
		Incorrect upload or configuration may require a factory reset or re-flashing of your ESP.<br>
		For security reasons, passwords are not backed up.</div>
		<a class="btn lnk" id="bckcfg" href="/presets.json" download="presets">Backup presets</a><br>
		<div>Restore presets<br><input type="file" name="data" accept=".json"> <button type="button" onclick="uploadFile(d.Sf.data,'/presets.json');">Upload</button><br></div><br>
		<a class="btn lnk" id="bckpresets" href="/cfg.json" download="cfg">Backup configuration</a><br>
		<div>Restore configuration<br><input type="file" name="data2" accept=".json"> <button type="button" onclick="uploadFile(d.Sf.data2,'/cfg.json');">Upload</button><br></div>
		<hr>
		<h3>About</h3>
		<a href="https://github.com/wled-dev/WLED/" target="_blank">WLED</a>&#32;version ##VERSION##<!-- Autoreplaced from package.json --><br><br>
		<a href="https://kno.wled.ge/about/contributors/" target="_blank">Contributors, dependencies and special thanks</a><br>
		A huge thank you to everyone who helped me create WLED!<br><br>
		(c) 2016-2024 Christian Schwinne <br>
		<i>Licensed under the <a href="https://github.com/wled-dev/WLED/blob/main/LICENSE" target="_blank">EUPL v1.2 license</a></i><br><br>
		Installed version: <span class="sip">WLED ##VERSION##</span><hr>
		<div id="toast"></div>
		<button type="button" onclick="B()">Back</button><button type="submit">Save</button>
	</form>
</body>
</html>
