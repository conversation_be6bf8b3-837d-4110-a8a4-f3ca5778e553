html {
	touch-action: manipulation;
}
body {
  font-family: Verdana, sans-serif;
  font-size: 1rem;
  text-align: center;
  background: #222;
  color: #fff;
  line-height: 200%;
  margin: 0;
}
hr {
  border-color: #666;
}
hr.sml {
  width: 260px;
}
h4 {
  margin: 0;
}
a, a:hover {
  color: #28f;
  text-decoration: none;
}
button, .btn {
  background: #333;
  color: #fff;
  font-family: Verdana, sans-serif;
  border: 0.3ch solid #333;
	border-radius: 24px;
  display: inline-block;
  font-size: 20px;
  margin: 12px 8px 8px;
  padding: 8px 12px;
  min-width: 48px;
  cursor: pointer;
  text-decoration: none;
}
button.sml {
  padding: 8px;
  border-radius: 20px;
  font-size: 15px;
  min-width: 40px;
  margin: 0 0 0 10px;
}
#scan {
  margin-top: -10px;
}
.toprow {
  top: 0;
  position: sticky;
  background-color:#222;
  z-index:1;
}
.lnk {
  border: 0;
}
.helpB {
  text-align: left;
  position: absolute;
  width: 60px;
}
.hide {
  display: none;
}
.err {
  color: #f00;
}
.warn {
  color: #fa0;
}
input {
  background: #333;
  color: #fff;
  font-family: Verdana, sans-serif;
  border: 0.5ch solid #333;
}
input:disabled {
  color: #888;
}
input:invalid {
  color: #f00;
}
input[type="text"],
input[type="number"],
input[type="password"],
select {
    font-size: medium;
    margin: 2px;
  }
input[type="number"] {
  width: 4em;
}
input[type="number"].xxl {
  width: 100px;
}
input[type="number"].xl {
  width: 85px;
}
input[type="number"].l {
  width: 64px;
}
input[type="number"].m {
  width: 56px;
}
input[type="number"].s {
  width: 48px;
}
input[type="number"].xs {
  width: 40px;
}
input[type="checkbox"] {
  transform: scale(1.5);
  margin-right: 10px;
}
td input[type="checkbox"] {
  margin-right: revert;
}
input[type=file] {
  font-size: 16px
}
select {
  margin: 2px;
  background: #333;
  color: #fff;
  font-family: Verdana, sans-serif;
  border: 0.5ch solid #333;
}
select.pin {
  max-width: 120px;
  text-overflow: ellipsis;
}
tr {
  line-height: 100%;
}
td {
  padding: 2px;
}
.d5 {
  width: 4rem !important;
}
.cal {
  font-size:1.5rem;
  cursor:pointer
}
#TMT table {
  width: 100%;
}

#msg {
  display: none;
}

#toast {
	opacity: 0;
	background-color: #444;
	border-radius: 5px;
	bottom: 64px;
	color: #fff;
	font-size: 17px;
	padding: 16px;
	pointer-events: none;
	position: fixed;
	text-align: center;
	z-index: 5;
	transform: translateX(-50%);
  max-width: 90%;
	left: 50%;
}

#toast.show {
	opacity: 1;
	background-color: #264;
	animation: fadein 0.5s, fadein 0.5s 2.5s reverse;
}

#toast.error {
	opacity: 1;
	background-color: #b21;
	animation: fadein 0.5s;
}

@media screen and (max-width: 767px) {
  input[type="text"],
  input[type="file"],
  input[type="number"],
  input[type="email"],
  input[type="tel"],
  input[type="password"] {
    font-size: 16px;
  }
}

@media screen and (max-width: 480px) {
  input[type="number"].s {
    width: 40px;
  }
  input[type="number"].xs {
    width: 32px;
  }
  input[type="file"] {
    width: 224px;
  }
  #btns select {
    width: 144px;
  }
}
