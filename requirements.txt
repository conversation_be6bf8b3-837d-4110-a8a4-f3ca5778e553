#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile requirements.in
#
ajsonrpc==1.2.0
    # via platformio
anyio==4.8.0
    # via starlette
bottle==0.13.2
    # via platformio
certifi==2025.1.31
    # via requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   platformio
    #   uvicorn
colorama==0.4.6
    # via platformio
h11==0.16.0
    # via
    #   uvicorn
    #   wsproto
idna==3.10
    # via
    #   anyio
    #   requests
marshmallow==3.26.1
    # via platformio
packaging==24.2
    # via marshmallow
platformio==6.1.17
    # via -r requirements.in
pyelftools==0.32
    # via platformio
pyserial==3.5
    # via platformio
requests==2.32.4
    # via platformio
semantic-version==2.10.0
    # via platformio
sniffio==1.3.1
    # via anyio
starlette==0.45.3
    # via platformio
tabulate==0.9.0
    # via platformio
typing-extensions==4.12.2
    # via anyio
urllib3==2.5.0
    # via requests
uvicorn==0.34.0
    # via platformio
wsproto==1.2.0
    # via platformio
