name: WLED Release CI

on:
  push:
    tags:
      - '*'

jobs:
  
  wled_build:
    uses: ./.github/workflows/build.yml
    
  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: wled_build
    steps:
    - uses: actions/download-artifact@v4
      with:
        merge-multiple: true
    - name: "✏️ Generate release changelog"
      id: changelog
      uses: jan<PERSON><PERSON><PERSON><PERSON>/action-github-changelog-generator@v2.3
      with:
          token: ${{ secrets.GITHUB_TOKEN }} 
          sinceTag: v0.15.0
          maxIssues: 500
          # Exclude issues that were closed without resolution from changelog
          exclude-labels: 'stale,wontfix,duplicate,invalid'       
    - name: Create draft release
      uses: softprops/action-gh-release@v1
      with:
        body: ${{ steps.changelog.outputs.changelog }}
        draft: True
        files: |
          *.bin
          *.bin.gz

