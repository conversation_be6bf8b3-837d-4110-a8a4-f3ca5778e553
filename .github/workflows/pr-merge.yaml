    name: Notify Discord on PR Merge
    on:
      workflow_dispatch:
      pull_request_target:
        types: [closed]

    jobs:
      notify:
        runs-on: ubuntu-latest
        if: github.event.pull_request.merged == true
        steps:
        - name: Get User Permission
          id: checkAccess
          uses: actions-cool/check-user-permission@v2
          with:
            require: write
            username: ${{ github.triggering_actor }}
          env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        - name: Check User Permission
          if: steps.checkAccess.outputs.require-result == 'false'
          run: |
            echo "${{ github.triggering_actor }} does not have permissions on this repo."
            echo "Current permission level is ${{ steps.checkAccess.outputs.user-permission }}"
            echo "Job originally triggered by ${{ github.actor }}"
            exit 1
        - name: Send Discord notification
          env:
            PR_NUMBER: ${{ github.event.pull_request.number }}
            PR_TITLE: ${{ github.event.pull_request.title }}
            PR_URL: ${{ github.event.pull_request.html_url }}
            ACTOR: ${{ github.actor }}
          run: |
            jq -n \
              --arg content "Pull Request #${PR_NUMBER} \"${PR_TITLE}\" merged by ${ACTOR}
            ${PR_URL}. It will be included in the next nightly builds, please test" \
              '{content: $content}' \
              | curl -H "Content-Type: application/json" -d @- ${{ secrets.DISCORD_WEBHOOK_BETA_TESTERS }}
