#pragma once

#include <TFT_eSPI.h>

extern TFT_eSPI tft;

// The following functions are generated by:
// usermods/pixels_dice_tray/generate_roll_info.py

// GENERATED
static void PrintRoll0() {
  tft.setTextColor(63488);
  tft.println("Barb Chain");
  tft.setTextColor(65535);
  tft.println("Atk/CMD 12");
  tft.println("Range: 70");
  tft.setTextSize(1);
  tft.println("Summon 3 chains. Make");
  tft.println("a melee atk 1d6 or a ");
  tft.println("trip CMD=AT. On a hit");
  tft.println("make Will save or sha");
  tft.println("ken 1d4 rnds.");
}

static void PrintRoll1() {
  tft.setTextColor(2016);
  tft.println("Saves");
  tft.setTextColor(65535);
  tft.println("FORT 8");
  tft.println("REFLEX 8");
  tft.println("WILL 9");
}

static void PrintRoll2() {
  tft.println("Skill");
}

static void PrintRoll3() {
  tft.println("Attack");
  tft.println("Melee +9");
  tft.println("Range +6");
}

static void PrintRoll4() {
  tft.println("Cure");
  tft.println("Lit 1d8+5");
  tft.println("Mod 2d8+9");
  tft.println("Ser 3d8+9");
}

static void PrintRoll5() {
  tft.println("Concentrat");
  tft.println("+15");
  tft.setTextSize(1);
  tft.println("Defensive 15+2*SP_LV");
  tft.println("Dmg 10+DMG+SP_LV");
  tft.println("Grapple 10+CMB+SP_LV");
}

static const char* GetRollName(uint8_t key) {
  switch (key) {
    case 0:
      return "Barb Chain";
    case 1:
      return "Saves";
    case 2:
      return "Skill";
    case 3:
      return "Attack";
    case 4:
      return "Cure";
    case 5:
      return "Concentrate";
  }
  return "";
}

static void PrintRollInfo(uint8_t key) {
  tft.setTextColor(TFT_WHITE);
  tft.setCursor(0, 0);
  tft.setTextSize(2);
  switch (key) {
    case 0:
      PrintRoll0();
      return;
    case 1:
      PrintRoll1();
      return;
    case 2:
      PrintRoll2();
      return;
    case 3:
      PrintRoll3();
      return;
    case 4:
      PrintRoll4();
      return;
    case 5:
      PrintRoll5();
      return;
  }
  tft.setTextColor(TFT_RED);
  tft.setCursor(0, 60);
  tft.println("Unknown");
}

static constexpr size_t NUM_ROLL_INFOS = 6;
