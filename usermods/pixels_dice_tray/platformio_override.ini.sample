[platformio]
default_envs = t_qt_pro_8MB_dice, esp32s3dev_8MB_qspi_dice

# ------------------------------------------------------------------------------
# T-QT Pro 8MB with integrated 128x128 TFT screen
# ------------------------------------------------------------------------------
[env:t_qt_pro_8MB_dice]
board = esp32-s3-devkitc-1 ;; generic dev board;
platform = ${esp32s3.platform}
upload_speed = 921600
build_unflags = ${common.build_unflags}
board_build.partitions = ${esp32.large_partitions}
board_build.f_flash = 80000000L
board_build.flash_mode = qio
monitor_filters = esp32_exception_decoder
build_flags = ${common.build_flags} ${esp32s3.build_flags} -D WLED_RELEASE_NAME=T-QT-PRO-8MB
  -D CONFIG_LITTLEFS_FOR_IDF_3_2 -D WLED_WATCHDOG_TIMEOUT=0
  -D ARDUINO_USB_CDC_ON_BOOT=1 -D ARDUINO_USB_MODE=1      ;; for boards with USB-OTG connector only (USBCDC or "TinyUSB")
  
  -D USERMOD_PIXELS_DICE_TRAY ;; Enables this UserMod
  -D USERMOD_PIXELS_DICE_TRAY_BL_ACTIVE_LOW=1
  -D USERMOD_PIXELS_DICE_TRAY_ROTATION=2

  ;-D WLED_DEBUG
  ;;;;;;;;;;;;;;;;;; TFT_eSPI Settings ;;;;;;;;;;;;;;;;;;;;;;;;
  ;-DCORE_DEBUG_LEVEL=ARDUHAL_LOG_LEVEL_DEBUG
  -D USER_SETUP_LOADED=1
  
  ; Define the TFT driver, pins etc. from: https://github.com/Bodmer/TFT_eSPI/blob/master/User_Setups/Setup211_LilyGo_T_QT_Pro_S3.h
  ; GC9A01 128 x 128 display with no chip select line
  -D USER_SETUP_ID=211
  -D GC9A01_DRIVER=1
  -D TFT_WIDTH=128
  -D TFT_HEIGHT=128

  -D TFT_BACKLIGHT_ON=0
  -D TFT_ROTATION=3
  -D CGRAM_OFFSET=1

  -D TFT_MISO=-1
  -D TFT_MOSI=2
  -D TFT_SCLK=3
  -D TFT_CS=5
  -D TFT_DC=6
  -D TFT_RST=1
  -D TFT_BL=10
  -D LOAD_GLCD=1
  -D LOAD_FONT2=1
  -D LOAD_FONT4=1
  -D LOAD_FONT6=1
  -D LOAD_FONT7=1
  -D LOAD_FONT8=1
  -D LOAD_GFXFF=1
  ; Avoid SPIFFS dependancy that was causing compile issues.
  ;-D SMOOTH_FONT=1
  -D SPI_FREQUENCY=40000000
  -D SPI_READ_FREQUENCY=20000000
  -D SPI_TOUCH_FREQUENCY=2500000

lib_deps = ${esp32s3.lib_deps}
  ${esp32.AR_lib_deps}
  ESP32 BLE Arduino
  bodmer/TFT_eSPI @ 2.5.43
  axlan/pixels-dice-interface @ 1.2.0

# ------------------------------------------------------------------------------
# ESP32S3 dev board with 8MB flash and no extended RAM.
# ------------------------------------------------------------------------------
[env:esp32s3dev_8MB_qspi_dice]
board = esp32-s3-devkitc-1 ;; generic dev board;
platform = ${esp32s3.platform}
upload_speed = 921600
build_unflags = ${common.build_unflags}
board_build.partitions = ${esp32.large_partitions}
board_build.f_flash = 80000000L
board_build.flash_mode = qio
monitor_filters = esp32_exception_decoder
build_flags = ${common.build_flags} ${esp32s3.build_flags} -D WLED_RELEASE_NAME=ESP32-S3_8MB_qspi
  -D CONFIG_LITTLEFS_FOR_IDF_3_2 -D WLED_WATCHDOG_TIMEOUT=0
  -D ARDUINO_USB_CDC_ON_BOOT=1 -D ARDUINO_USB_MODE=1      ;; for boards with USB-OTG connector only (USBCDC or "TinyUSB")
  
  -D USERMOD_PIXELS_DICE_TRAY ;; Enables this UserMod

  ;-D WLED_DEBUG
lib_deps = ${esp32s3.lib_deps}
  ${esp32.AR_lib_deps}
  ESP32 BLE Arduino
  axlan/pixels-dice-interface @ 1.2.0

# ------------------------------------------------------------------------------
# ESP32 dev board without screen
# ------------------------------------------------------------------------------
# THIS DOES NOT WORK!!!!!!
# While it builds and programs onto the device, I ran into a series of issues
# trying to actually run.
# Right after the AP init there's an allocation exception which claims to be in
# the UDP server. There seems to be a ton of heap remaining, so the exact error
# might be a red herring.
# It appears that the BLE scanning task is conflicting with the networking tasks.
# I was successfully running simple applications with the pixels-dice-interface
# on ESP32 dev boards, so it may be an issue with too much being scheduled in
# parallel. Also not clear exactly what difference between the ESP32 and the
# ESP32S3 would be causing this, though they do run different BLE versions.
# May be related to some of the issues discussed in:
# https://github.com/wled-dev/WLED/issues/1382
; [env:esp32dev_dice]
; extends = env:esp32dev
; build_flags = ${common.build_flags} ${esp32.build_flags} -D WLED_RELEASE_NAME=ESP32
;   ; Enable Pixels dice mod
;   -D USERMOD_PIXELS_DICE_TRAY
; lib_deps = ${esp32.lib_deps}
;   ESP32 BLE Arduino
;   axlan/pixels-dice-interface @ 1.2.0
; ; Tiny file system partition, no core dump to fit BLE library.
; board_build.partitions = usermods/pixels_dice_tray/WLED_ESP32_4MB_64KB_FS.csv
