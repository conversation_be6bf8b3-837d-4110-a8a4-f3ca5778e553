[platformio]
default_envs = usermods_esp32, usermods_esp32c3, usermods_esp32s2, usermods_esp32s3

[env:usermods_esp32]
extends = env:esp32dev
custom_usermods = ${usermods.custom_usermods}
board_build.partitions = ${esp32.extreme_partitions}  ; We're gonna need a bigger boat


[env:usermods_esp32c3]
extends = env:esp32c3dev
board = esp32-c3-devkitm-1
custom_usermods = ${usermods.custom_usermods}
board_build.partitions = ${esp32.extreme_partitions}  ; We're gonna need a bigger boat


[env:usermods_esp32s2]
extends = env:lolin_s2_mini
custom_usermods = ${usermods.custom_usermods}
board_build.partitions = ${esp32.extreme_partitions}  ; We're gonna need a bigger boat


[env:usermods_esp32s3]
extends = env:esp32s3dev_16MB_opi
custom_usermods = ${usermods.custom_usermods}
board_build.partitions = ${esp32.extreme_partitions}  ; We're gonna need a bigger boat



[usermods]
# Added in CI
