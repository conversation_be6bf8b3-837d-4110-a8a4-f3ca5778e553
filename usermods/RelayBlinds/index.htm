<!DOCTYPE html>
<html>
<head><meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
  <meta charset="utf-8">
  <title>Blinds</title>
  <script>
      strA = "";
    function send()
    {
      nocache = "&nocache=" + Math.random() * 1000000;
      var request = new XMLHttpRequest();
      // send HTTP request
      request.open("GET", "win/" + strA +nocache, true);
      request.send(null);
      strA = "";
    }
    function up()
    {
      strA = "&U0=2";
      send();
    }
    function down()
    {
      strA = "&U0=1";
      send();
    }
    function OpenSettings()
    {
      window.open("/settings", "_self");
    }
  </script>
  <style>
    body {
      text-align: center;
      background: linear-gradient(45deg,#0ca,#0ac);
      height: 100%;
      margin: 0;
      background-repeat: no-repeat;
      background-attachment: fixed;
    }
    html {
      height: 100%;
    }
    svg {
      width: 30vw;
      padding: 2vh;
    }
    .tool_box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  </style>
<style id="holderjs-style" type="text/css"></style></head>
<body class=" __plain_text_READY__">
<svg style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
<symbol id="icon-box-add" viewBox="0 0 32 32">
<path d="M26 2h-20l-6 6v21c0 0.552 0.448 1 1 1h30c0.552 0 1-0.448 1-1v-21l-6-6zM16 26l-10-8h6v-6h8v6h6l-10 8zM4.828 6l2-2h18.343l2 2h-22.343z"></path>
</symbol>
<symbol id="icon-box-remove" viewBox="0 0 32 32">
<path d="M26 2h-20l-6 6v21c0 0.552 0.448 1 1 1h30c0.552 0 1-0.448 1-1v-21l-6-6zM20 20v6h-8v-6h-6l10-8 10 8h-6zM4.828 6l2-2h18.343l2 2h-22.343z"></path>
</symbol>
<symbol id="icon-cog" viewBox="0 0 32 32">
<path d="M29.181 19.070c-1.679-2.908-0.669-6.634 2.255-8.328l-3.145-5.447c-0.898 0.527-1.943 0.829-3.058 0.829-3.361 0-6.085-2.742-6.085-6.125h-6.289c0.008 1.044-0.252 2.103-0.811 3.070-1.679 2.908-5.411 3.897-8.339 2.211l-3.144 5.447c0.905 0.515 1.689 1.268 2.246 2.234 1.676 2.903 0.672 6.623-2.241 8.319l3.145 5.447c0.895-0.522 1.935-0.82 3.044-0.82 3.35 0 6.067 2.725 6.084 6.092h6.289c-0.003-1.034 0.259-2.080 0.811-3.038 1.676-2.903 5.399-3.894 8.325-2.219l3.145-5.447c-0.899-0.515-1.678-1.266-2.232-2.226zM16 22.479c-3.578 0-6.479-2.901-6.479-6.479s2.901-6.479 6.479-6.479c3.578 0 6.479 2.901 6.479 6.479s-2.901 6.479-6.479 6.479z"></path>
</symbol>
</defs>
</svg>
  <div id="tbB" class="tool_box">
    <svg id="upb" onclick="up()"><use xlink:href="#icon-box-remove"></use></svg>
    <svg id="dnb" onclick="down()"><use xlink:href="#icon-box-add"></use></svg>
    <svg id="stb" onclick="OpenSettings()"><use xlink:href="#icon-cog"></use></svg>
  </div>
</body>
</html>