# Shift Light for Project Cars

Turn your WLED lights into a rev light and shift indicator for Project Cars.
It's easy to use.

_1._ Make sure your WLED device and your PC/console are on the same network and can talk to each other

_2._ Go to the gameplay settings menu in PCARS and enable UDP. There are 9 numbers you can choose from. This is the refresh rate. The lower the number, the better. However, you might run into problems at faster rates.

| Number | Updates/Second |
| ------ | -------------- |
| 1      | 60             |
| 2      | 50             |
| 3      | 40             |
| 4      | 30             |
| 5      | 20             |
| 6      | 15             |
| 7      | 10             |
| 8      | 05             |
| 9      | 1              |

_3._ Once you enter a race, WLED should automatically shift to PCARS mode.
_4._ Done.
