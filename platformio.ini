; PlatformIO Project Configuration File
; Please visit documentation: https://docs.platformio.org/page/projectconf.html

[platformio]
# ------------------------------------------------------------------------------
# ENVIRONMENTS
#
# Please uncomment one of the lines below to select your board(s)
# (use `platformio_override.ini` when building for your own board; see `platformio_override.ini.sample` for an example)
# ------------------------------------------------------------------------------

# CI/release binaries
default_envs = nodemcuv2, esp8266_2m, esp01_1m_full, nodemcuv2_160, esp8266_2m_160, esp01_1m_full_160, nodemcuv2_compat, esp8266_2m_compat, esp01_1m_full_compat, esp32dev, esp32_eth, lolin_s2_mini, esp32c3dev, esp32s3dev_16MB_opi, esp32s3dev_8MB_opi, esp32s3_4M_qspi, esp32_wrover, usermods

src_dir  = ./wled00
data_dir = ./wled00/data
build_cache_dir = ~/.buildcache
extra_configs =
  platformio_override.ini

[common]
# ------------------------------------------------------------------------------
# PLATFORM:
#   !! DO NOT confuse platformio's ESP8266 development platform with Arduino core for ESP8266
#
#   arduino core 2.6.3 = platformIO 2.3.2
#   arduino core 2.7.0 = platformIO 2.5.0
# ------------------------------------------------------------------------------
arduino_core_2_6_3 = espressif8266@2.3.3
arduino_core_2_7_4 = espressif8266@2.6.2
arduino_core_3_0_0 = espressif8266@3.0.0
arduino_core_3_0_2 = espressif8266@3.2.0
arduino_core_3_1_0 = espressif8266@4.1.0
arduino_core_3_1_2 = espressif8266@4.2.1

# Development platforms
arduino_core_develop = https://github.com/platformio/platform-espressif8266#develop
arduino_core_git = https://github.com/platformio/platform-espressif8266#feature/stage

# Platform to use for ESP8266
platform_wled_default = ${common.arduino_core_3_1_2}
# We use 2.7.4.7 for all, includes PWM flicker fix and Wstring optimization
#platform_packages = tasmota/framework-arduinoespressif8266 @ 3.20704.7
platform_packages = platformio/toolchain-xtensa @ ~2.100300.220621 #2.40802.200502
                    platformio/tool-esptool #@ ~1.413.0
                    platformio/tool-esptoolpy #@ ~1.30000.0

## previous platform for 8266, in case of problems with the new one
## you'll need  makuna/NeoPixelBus@ 2.6.9 for arduino_core_3_0_2, which does not support Ucs890x
;; platform_wled_default = ${common.arduino_core_3_0_2}
;; platform_packages = tasmota/framework-arduinoespressif8266 @ 3.20704.7
;;                    platformio/toolchain-xtensa @ ~2.40802.200502
;;                    platformio/tool-esptool @ ~1.413.0
;;                    platformio/tool-esptoolpy @ ~1.30000.0

# ------------------------------------------------------------------------------
# FLAGS: DEBUG
# esp8266 : see https://docs.platformio.org/en/latest/platforms/espressif8266.html#debug-level
# esp32   : see https://docs.platformio.org/en/latest/platforms/espressif32.html#debug-level
# ------------------------------------------------------------------------------
debug_flags = -D DEBUG=1 -D WLED_DEBUG
  -DDEBUG_ESP_WIFI -DDEBUG_ESP_HTTP_CLIENT -DDEBUG_ESP_HTTP_UPDATE -DDEBUG_ESP_HTTP_SERVER -DDEBUG_ESP_UPDATER -DDEBUG_ESP_OTA -DDEBUG_TLS_MEM ;; for esp8266
  # if needed (for memleaks etc) also add; -DDEBUG_ESP_OOM -include "umm_malloc/umm_malloc_cfg.h"
  # -DDEBUG_ESP_CORE is not working right now

# ------------------------------------------------------------------------------
# FLAGS: ldscript (available ldscripts at https://github.com/esp8266/Arduino/tree/master/tools/sdk/ld)
#    ldscript_2m1m (2048 KB) = 1019 KB sketch, 4 KB eeprom, 1004 KB spiffs, 16 KB reserved
#    ldscript_4m1m (4096 KB) = 1019 KB sketch, 4 KB eeprom, 1002 KB spiffs, 16 KB reserved, 2048 KB empty/ota?
#
# Available lwIP variants (macros):
#    -DPIO_FRAMEWORK_ARDUINO_LWIP_HIGHER_BANDWIDTH  = v1.4 Higher Bandwidth (default)
#    -DPIO_FRAMEWORK_ARDUINO_LWIP2_LOW_MEMORY       = v2 Lower Memory
#    -DPIO_FRAMEWORK_ARDUINO_LWIP2_HIGHER_BANDWIDTH = v2 Higher Bandwidth
#    -DPIO_FRAMEWORK_ARDUINO_LWIP2_HIGHER_BANDWIDTH_LOW_FLASH
#
# BearSSL performance:
#  When building with -DSECURE_CLIENT=SECURE_CLIENT_BEARSSL, please add `board_build.f_cpu = 160000000` to the environment configuration
#
# BearSSL ciphers:
#   When building on core >= 2.5, you can add the build flag -DBEARSSL_SSL_BASIC in order to build BearSSL with a limited set of ciphers:
#     TLS_RSA_WITH_AES_128_CBC_SHA256 / AES128-SHA256
#     TLS_RSA_WITH_AES_256_CBC_SHA256 / AES256-SHA256
#     TLS_RSA_WITH_AES_128_CBC_SHA / AES128-SHA
#     TLS_RSA_WITH_AES_256_CBC_SHA / AES256-SHA
#  This reduces the OTA size with ~45KB, so it's especially useful on low memory boards (512k/1m).
# ------------------------------------------------------------------------------
build_flags =
  -DMQTT_MAX_PACKET_SIZE=1024
  -DSECURE_CLIENT=SECURE_CLIENT_BEARSSL
  -DBEARSSL_SSL_BASIC
  -D CORE_DEBUG_LEVEL=0
  -D NDEBUG
  -Wno-attributes ;; silence warnings about unknown attribute 'maybe_unused' in NeoPixelBus
  #build_flags for the IRremoteESP8266 library (enabled decoders have to appear here)
  -D _IR_ENABLE_DEFAULT_=false
  -D DECODE_HASH=true
  -D DECODE_NEC=true
  -D DECODE_SONY=true
  -D DECODE_SAMSUNG=true
  -D DECODE_LG=true
  -DWLED_USE_MY_CONFIG

build_unflags =

ldscript_1m128k = eagle.flash.1m128.ld
ldscript_2m512k = eagle.flash.2m512.ld
ldscript_2m1m = eagle.flash.2m1m.ld
ldscript_4m1m = eagle.flash.4m1m.ld

[scripts_defaults]
extra_scripts =
  pre:pio-scripts/set_version.py
  post:pio-scripts/output_bins.py
  post:pio-scripts/strip-floats.py
  pre:pio-scripts/user_config_copy.py
  pre:pio-scripts/load_usermods.py
  pre:pio-scripts/build_ui.py
  post:pio-scripts/validate_modules.py  ;; double-check the build output usermods
  ; post:pio-scripts/obj-dump.py  ;; convenience script to create a disassembly dump of the firmware (hardcore debugging)

# ------------------------------------------------------------------------------
# COMMON SETTINGS:
# ------------------------------------------------------------------------------
[env]
framework = arduino
board_build.flash_mode = dout
monitor_speed = 115200
# slow upload speed but most compatible (use platformio_override.ini to use faster speed)
upload_speed = 115200

# ------------------------------------------------------------------------------
# LIBRARIES: required dependencies
#   Please note that we don't always use the latest version of a library.
#
#   The following libraries have been included (and some of them changed) in the source:
#     ArduinoJson@5.13.5, E131@1.0.0(changed), Time@1.5, Timezone@1.2.1
# ------------------------------------------------------------------------------
lib_compat_mode = strict
lib_deps =
    fastled/FastLED @ 3.6.0
    IRremoteESP8266 @ 2.8.2
    makuna/NeoPixelBus @ 2.8.3
    #https://github.com/makuna/NeoPixelBus.git#CoreShaderBeta
    https://github.com/Aircoookie/ESPAsyncWebServer.git#v2.4.2
    marvinroger/AsyncMqttClient @ 0.9.0
  # for I2C interface
    ;Wire
  # ESP-NOW library
    ;gmag11/QuickESPNow @ ~0.7.0
    https://github.com/blazoncek/QuickESPNow.git#optional-debug
  #For use of the TTGO T-Display ESP32 Module with integrated TFT display uncomment the following line
    #TFT_eSPI
  #For compatible OLED display uncomment following
    #olikraus/U8g2 #@ ~2.33.15
  #For Dallas sensor uncomment following
    #paulstoffregen/OneWire @ ~2.3.8
  #For BME280 sensor uncomment following
    #BME280 @ ~3.0.0
    ;adafruit/Adafruit BMP280 Library @ 2.1.0
    ;adafruit/Adafruit CCS811 Library @ 1.0.4
    ;adafruit/Adafruit Si7021 Library @ 1.4.0
  #For MAX1704x Lipo Monitor / Fuel Gauge uncomment following
    ; https://github.com/adafruit/Adafruit_BusIO @ 1.14.5
    ; https://github.com/adafruit/Adafruit_MAX1704X @ 1.0.2
  #For MPU6050 IMU uncomment follwoing
    ;electroniccats/MPU6050 @1.0.1
  # SHT85
    ;robtillaart/SHT85@~0.3.3

extra_scripts = ${scripts_defaults.extra_scripts}

[esp8266]
build_unflags = ${common.build_unflags}
build_flags =
  -DESP8266
  -DFP_IN_IROM
  ;-Wno-deprecated-declarations
  ;-Wno-register  ;; leaves some warnings when compiling C files: command-line option '-Wno-register' is valid for C++/ObjC++ but not for C
  ;-Dregister= # remove warnings in C++17 due to use of deprecated register keyword by the FastLED library ;; warning: this can be dangerous
  -Wno-misleading-indentation
  ; NONOSDK22x_190703 = 2.2.2-dev(38a443e)
  -DPIO_FRAMEWORK_ARDUINO_ESPRESSIF_SDK22x_190703
  ; lwIP 2 - Higher Bandwidth no Features
  ;  -DPIO_FRAMEWORK_ARDUINO_LWIP2_HIGHER_BANDWIDTH_LOW_FLASH
  ; lwIP 1.4 - Higher Bandwidth (Aircoookie has)
  -DPIO_FRAMEWORK_ARDUINO_LWIP_HIGHER_BANDWIDTH
  ; VTABLES in Flash
  -DVTABLES_IN_FLASH
  ; restrict to minimal mime-types
  -DMIMETYPE_MINIMAL
  ; other special-purpose framework flags (see https://docs.platformio.org/en/latest/platforms/espressif8266.html)
  ; decrease code cache size and increase IRAM to fit all pixel functions
  -D PIO_FRAMEWORK_ARDUINO_MMU_CACHE16_IRAM48 ;; in case of linker errors like "section `.text1' will not fit in region `iram1_0_seg'"
  ; -D PIO_FRAMEWORK_ARDUINO_MMU_CACHE16_IRAM48_SECHEAP_SHARED ;; (experimental) adds some extra heap, but may cause slowdown
  -D NON32XFER_HANDLER ;; ask forgiveness for PROGMEM misuse

lib_deps =
  #https://github.com/lorol/LITTLEFS.git
  ESPAsyncTCP @ 1.2.2
  ESPAsyncUDP
  ESP8266PWM
  ${env.lib_deps}

;; compatibilty flags - same as 0.14.0 which seems to work better on some 8266 boards. Not using PIO_FRAMEWORK_ARDUINO_MMU_CACHE16_IRAM48
build_flags_compat =
  -DESP8266
  -DFP_IN_IROM
  ;;-Wno-deprecated-declarations
  -Wno-misleading-indentation
  ;;-Wno-attributes ;; silence warnings about unknown attribute 'maybe_unused' in NeoPixelBus
  -DPIO_FRAMEWORK_ARDUINO_ESPRESSIF_SDK22x_190703
  -DPIO_FRAMEWORK_ARDUINO_LWIP_HIGHER_BANDWIDTH
  -DVTABLES_IN_FLASH
  -DMIMETYPE_MINIMAL
  -DWLED_SAVE_IRAM ;; needed to prevent linker error

;; this platform version was used for WLED 0.14.0
platform_compat = espressif8266@4.2.0
platform_packages_compat =
                    platformio/toolchain-xtensa @ ~2.100300.220621 #2.40802.200502
                    platformio/tool-esptool #@ ~1.413.0
                    platformio/tool-esptoolpy #@ ~1.30000.0

;; experimental - for using older NeoPixelBus 2.7.9
lib_deps_compat =
  ESPAsyncTCP @ 1.2.2
  ESPAsyncUDP
  ESP8266PWM
  fastled/FastLED @ 3.6.0
  IRremoteESP8266 @ 2.8.2
  makuna/NeoPixelBus @ 2.7.9
  https://github.com/blazoncek/QuickESPNow.git#optional-debug
  https://github.com/Aircoookie/ESPAsyncWebServer.git#v2.4.0

[esp32_all_variants]
lib_deps =
  esp32async/AsyncTCP @ 3.4.7
  bitbank2/AnimatedGIF@^1.4.7
  https://github.com/Aircoookie/GifDecoder#bc3af18
build_flags =
  -D CONFIG_ASYNC_TCP_USE_WDT=0
  -D CONFIG_ASYNC_TCP_STACK_SIZE=8192
  -D WLED_ENABLE_GIF

[esp32]
platform = ${esp32_idf_V4.platform}
platform_packages =
build_unflags = ${common.build_unflags}
build_flags = ${esp32_idf_V4.build_flags}
lib_deps = ${esp32_idf_V4.lib_deps}

tiny_partitions = tools/WLED_ESP32_2MB_noOTA.csv
default_partitions = tools/WLED_ESP32_4MB_1MB_FS.csv
extended_partitions = tools/WLED_ESP32_4MB_700k_FS.csv
big_partitions = tools/WLED_ESP32_4MB_256KB_FS.csv     ;; 1.8MB firmware, 256KB filesystem, coredump support
large_partitions = tools/WLED_ESP32_8MB.csv
extreme_partitions = tools/WLED_ESP32_16MB_9MB_FS.csv

board_build.partitions = ${esp32.default_partitions}   ;; default partioning for 4MB Flash - can be overridden in build envs
# additional build flags for audioreactive - must be applied globally
AR_build_flags = ;; -fsingle-precision-constant ;; forces ArduinoFFT to use float math (2x faster)
AR_lib_deps =  ;; for pre-usermod-library platformio_override compatibility


[esp32_idf_V4]
;; build environment for ESP32 using ESP-IDF 4.4.x / arduino-esp32 v2.0.5
;;
;; please note that you can NOT update existing ESP32 installs with a "V4" build. Also updating by OTA will not work properly.
;; You need to completely erase your device (esptool erase_flash) first, then install the "V4" build from VSCode+platformio.

;; select arduino-esp32 v2.0.9 (arduino-esp32 2.0.10 thru 2.0.14 are buggy so avoid them)
platform = https://github.com/tasmota/platform-espressif32/releases/download/2023.06.02/platform-espressif32.zip ;; Tasmota Arduino Core 2.0.9 with IPv6 support, based on IDF 4.4.4
build_unflags = ${common.build_unflags}
build_flags = -g
  -Wshadow=compatible-local ;; emit warning in case a local variable "shadows" another local one
  -DARDUINO_ARCH_ESP32 -DESP32
  ${esp32_all_variants.build_flags}
  -D WLED_ENABLE_DMX_INPUT
lib_deps =
  ${esp32_all_variants.lib_deps}
  https://github.com/someweisguy/esp_dmx.git#47db25d
  ${env.lib_deps}

[esp32s2]
;; generic definitions for all ESP32-S2 boards
platform = ${esp32_idf_V4.platform}
build_unflags = ${common.build_unflags}
build_flags = -g
  -DARDUINO_ARCH_ESP32
  -DARDUINO_ARCH_ESP32S2
  -DCONFIG_IDF_TARGET_ESP32S2=1
  -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0
  -DCO
  -DARDUINO_USB_MODE=0 ;; this flag is mandatory for ESP32-S2 !
  ;; please make sure that the following flags are properly set (to 0 or 1) by your board.json, or included in your custom platformio_override.ini entry:
  ;; ARDUINO_USB_CDC_ON_BOOT
  ${esp32_idf_V4.build_flags}
lib_deps =
  ${esp32_idf_V4.lib_deps}
board_build.partitions = ${esp32.default_partitions}   ;; default partioning for 4MB Flash - can be overridden in build envs

[esp32c3]
;; generic definitions for all ESP32-C3 boards
platform = ${esp32_idf_V4.platform}
build_unflags = ${common.build_unflags}
build_flags = -g
  -DARDUINO_ARCH_ESP32
  -DARDUINO_ARCH_ESP32C3
  -DCONFIG_IDF_TARGET_ESP32C3=1
  -DCO
  -DARDUINO_USB_MODE=1 ;; this flag is mandatory for ESP32-C3
  ;; please make sure that the following flags are properly set (to 0 or 1) by your board.json, or included in your custom platformio_override.ini entry:
  ;; ARDUINO_USB_CDC_ON_BOOT
  ${esp32_idf_V4.build_flags}
lib_deps =
  ${esp32_idf_V4.lib_deps}
board_build.partitions = ${esp32.default_partitions}   ;; default partioning for 4MB Flash - can be overridden in build envs
board_build.flash_mode = qio

[esp32s3]
;; generic definitions for all ESP32-S3 boards
platform = ${esp32_idf_V4.platform}
build_unflags = ${common.build_unflags}
build_flags = -g
  -DESP32
  -DARDUINO_ARCH_ESP32
  -DARDUINO_ARCH_ESP32S3
  -DCONFIG_IDF_TARGET_ESP32S3=1
  -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_DFU_ON_BOOT=0
  -DCO
  ;; please make sure that the following flags are properly set (to 0 or 1) by your board.json, or included in your custom platformio_override.ini entry:
  ;; ARDUINO_USB_MODE, ARDUINO_USB_CDC_ON_BOOT
  ${esp32_idf_V4.build_flags}
lib_deps =
  ${esp32_idf_V4.lib_deps}
board_build.partitions = ${esp32.large_partitions}   ;; default partioning for 8MB flash - can be overridden in build envs


# ------------------------------------------------------------------------------
# WLED BUILDS
# ------------------------------------------------------------------------------

[env:nodemcuv2]
board = nodemcuv2
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_RELEASE_NAME=\"ESP8266\" #-DWLED_DISABLE_2D
  -D WLED_DISABLE_PARTICLESYSTEM2D
lib_deps = ${esp8266.lib_deps}
monitor_filters = esp8266_exception_decoder

[env:nodemcuv2_compat]
extends = env:nodemcuv2
;; using platform version and build options from WLED 0.14.0
platform = ${esp8266.platform_compat}
platform_packages = ${esp8266.platform_packages_compat}
build_flags = ${common.build_flags} ${esp8266.build_flags_compat} -D WLED_RELEASE_NAME=\"ESP8266_compat\" #-DWLED_DISABLE_2D
  -D WLED_DISABLE_PARTICLESYSTEM2D
;; lib_deps = ${esp8266.lib_deps_compat} ;; experimental - use older NeoPixelBus 2.7.9

[env:nodemcuv2_160]
extends = env:nodemcuv2
board_build.f_cpu = 160000000L
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_RELEASE_NAME=\"ESP8266_160\" #-DWLED_DISABLE_2D
  -D WLED_DISABLE_PARTICLESYSTEM2D
custom_usermods = audioreactive

[env:esp8266_2m]
board = esp_wroom_02
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_RELEASE_NAME=\"ESP02\"
  -D WLED_DISABLE_PARTICLESYSTEM2D
  -D WLED_DISABLE_PARTICLESYSTEM1D
lib_deps = ${esp8266.lib_deps}

[env:esp8266_2m_compat]
extends = env:esp8266_2m
;; using platform version and build options from WLED 0.14.0
platform = ${esp8266.platform_compat}
platform_packages = ${esp8266.platform_packages_compat}
build_flags = ${common.build_flags} ${esp8266.build_flags_compat} -D WLED_RELEASE_NAME=\"ESP02_compat\" #-DWLED_DISABLE_2D
  -D WLED_DISABLE_PARTICLESYSTEM1D
  -D WLED_DISABLE_PARTICLESYSTEM2D

[env:esp8266_2m_160]
extends = env:esp8266_2m
board_build.f_cpu = 160000000L
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_RELEASE_NAME=\"ESP02_160\"
  -D WLED_DISABLE_PARTICLESYSTEM1D
  -D WLED_DISABLE_PARTICLESYSTEM2D
custom_usermods = audioreactive

[env:esp01_1m_full]
board = esp01_1m
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_1m128k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_RELEASE_NAME=\"ESP01\" -D WLED_DISABLE_OTA
  ; -D WLED_USE_REAL_MATH ;; may fix wrong sunset/sunrise times, at the cost of 7064 bytes FLASH and 975 bytes RAM
  -D WLED_DISABLE_PARTICLESYSTEM1D
  -D WLED_DISABLE_PARTICLESYSTEM2D
lib_deps = ${esp8266.lib_deps}

[env:esp01_1m_full_compat]
extends = env:esp01_1m_full
;; using platform version and build options from WLED 0.14.0
platform = ${esp8266.platform_compat}
platform_packages = ${esp8266.platform_packages_compat}
build_flags = ${common.build_flags} ${esp8266.build_flags_compat} -D WLED_RELEASE_NAME=\"ESP01_compat\" -D WLED_DISABLE_OTA #-DWLED_DISABLE_2D
  -D WLED_DISABLE_PARTICLESYSTEM1D
  -D WLED_DISABLE_PARTICLESYSTEM2D

[env:esp01_1m_full_160]
extends = env:esp01_1m_full
board_build.f_cpu = 160000000L
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_RELEASE_NAME=\"ESP01_160\" -D WLED_DISABLE_OTA
  ; -D WLED_USE_REAL_MATH ;; may fix wrong sunset/sunrise times, at the cost of 7064 bytes FLASH and 975 bytes RAM
  -D WLED_DISABLE_PARTICLESYSTEM1D
  -D WLED_DISABLE_PARTICLESYSTEM2D
custom_usermods = audioreactive

[env:esp32dev]
board = esp32dev
platform = ${esp32_idf_V4.platform}
build_unflags = ${common.build_unflags}
custom_usermods = audioreactive
build_flags = ${common.build_flags} ${esp32_idf_V4.build_flags} -D WLED_RELEASE_NAME=\"ESP32_V4\" #-D WLED_DISABLE_BROWNOUT_DET
              -DARDUINO_USB_CDC_ON_BOOT=0 ;; this flag is mandatory for "classic ESP32" when building with arduino-esp32 >=2.0.3
lib_deps = ${esp32_idf_V4.lib_deps}
monitor_filters = esp32_exception_decoder
board_build.partitions = ${esp32.default_partitions}
board_build.flash_mode = dio

[env:esp32dev_8M]
board = esp32dev
platform = ${esp32_idf_V4.platform}
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32_idf_V4.build_flags} -D WLED_RELEASE_NAME=\"ESP32_8M\" #-D WLED_DISABLE_BROWNOUT_DET
lib_deps = ${esp32_idf_V4.lib_deps}
monitor_filters = esp32_exception_decoder
board_build.partitions = ${esp32.large_partitions}
board_upload.flash_size = 8MB
board_upload.maximum_size = 8388608
; board_build.f_flash = 80000000L
; board_build.flash_mode = qio

[env:esp32dev_16M]
board = esp32dev
platform = ${esp32_idf_V4.platform}
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32_idf_V4.build_flags} -D WLED_RELEASE_NAME=\"ESP32_16M\" #-D WLED_DISABLE_BROWNOUT_DET
lib_deps = ${esp32_idf_V4.lib_deps}
monitor_filters = esp32_exception_decoder
board_build.partitions = ${esp32.extreme_partitions}
board_upload.flash_size = 16MB
board_upload.maximum_size = 16777216
board_build.f_flash = 80000000L
board_build.flash_mode = dio

[env:esp32_eth]
board = esp32-poe
platform = ${esp32_idf_V4.platform}
upload_speed = 921600
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32.build_flags} -D WLED_RELEASE_NAME=\"ESP32_Ethernet\" -D RLYPIN=-1 -D WLED_USE_ETHERNET -D BTNPIN=-1
;  -D WLED_DISABLE_ESPNOW ;; ESP-NOW requires wifi, may crash with ethernet only
lib_deps = ${esp32.lib_deps}
board_build.partitions = ${esp32.default_partitions}
board_build.flash_mode = dio

[env:esp32_wrover]
extends = esp32_idf_V4
board = ttgo-t7-v14-mini32
board_build.f_flash = 80000000L
board_build.flash_mode = qio
board_build.partitions = ${esp32.extended_partitions}
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32_idf_V4.build_flags} -D WLED_RELEASE_NAME=\"ESP32_WROVER\"
  -DBOARD_HAS_PSRAM -mfix-esp32-psram-cache-issue ;; Older ESP32 (rev.<3) need a PSRAM fix (increases static RAM used) https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-guides/external-ram.html
  -D DATA_PINS=25
lib_deps = ${esp32_idf_V4.lib_deps}
  
[env:esp32c3dev]
extends = esp32c3
platform = ${esp32c3.platform}
framework = arduino
board = esp32-c3-devkitm-1
board_build.partitions = ${esp32.default_partitions}
build_flags = ${common.build_flags} ${esp32c3.build_flags} -D WLED_RELEASE_NAME=\"ESP32-C3\"
  -D WLED_WATCHDOG_TIMEOUT=0
  -DLOLIN_WIFI_FIX ; seems to work much better with this
  -DARDUINO_USB_CDC_ON_BOOT=1 ;; for virtual CDC USB
  ;-DARDUINO_USB_CDC_ON_BOOT=0   ;; for serial-to-USB chip
upload_speed = 460800
build_unflags = ${common.build_unflags}
lib_deps = ${esp32c3.lib_deps}

[env:esp32s3dev_16MB_opi]
;; ESP32-S3 development board, with 16MB FLASH and >= 8MB PSRAM (memory_type: qio_opi)
board = esp32-s3-devkitc-1 ;; generic dev board; the next line adds PSRAM support
board_build.arduino.memory_type = qio_opi     ;; use with PSRAM: 8MB or 16MB
platform = ${esp32s3.platform}
upload_speed = 921600
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32s3.build_flags} -D WLED_RELEASE_NAME=\"ESP32-S3_16MB_opi\"
  -D CONFIG_LITTLEFS_FOR_IDF_3_2 -D WLED_WATCHDOG_TIMEOUT=0
  ;-D ARDUINO_USB_CDC_ON_BOOT=0  ;; -D ARDUINO_USB_MODE=1 ;; for boards with serial-to-USB chip
  -D ARDUINO_USB_CDC_ON_BOOT=1 -D ARDUINO_USB_MODE=1      ;; for boards with USB-OTG connector only (USBCDC or "TinyUSB")
  -DBOARD_HAS_PSRAM
lib_deps = ${esp32s3.lib_deps}
board_build.partitions = ${esp32.extreme_partitions}
board_upload.flash_size = 16MB
board_upload.maximum_size = 16777216
board_build.f_flash = 80000000L
board_build.flash_mode = qio
monitor_filters = esp32_exception_decoder

[env:esp32s3dev_8MB_opi]
;; ESP32-S3 development board, with 8MB FLASH and >= 8MB PSRAM (memory_type: qio_opi)
board = esp32-s3-devkitc-1 ;; generic dev board; the next line adds PSRAM support
board_build.arduino.memory_type = qio_opi     ;; use with PSRAM: 8MB or 16MB
platform = ${esp32s3.platform}
upload_speed = 921600
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32s3.build_flags} -D WLED_RELEASE_NAME=\"ESP32-S3_8MB_opi\"
  -D CONFIG_LITTLEFS_FOR_IDF_3_2 -D WLED_WATCHDOG_TIMEOUT=0
  ;-D ARDUINO_USB_CDC_ON_BOOT=0  ;; -D ARDUINO_USB_MODE=1 ;; for boards with serial-to-USB chip
  -D ARDUINO_USB_CDC_ON_BOOT=1 -D ARDUINO_USB_MODE=1      ;; for boards with USB-OTG connector only (USBCDC or "TinyUSB")
  -DBOARD_HAS_PSRAM
lib_deps = ${esp32s3.lib_deps}
board_build.partitions = ${esp32.large_partitions}
board_build.f_flash = 80000000L
board_build.flash_mode = qio
monitor_filters = esp32_exception_decoder

[env:esp32S3_wroom2]
;; For ESP32-S3 WROOM-2, a.k.a. ESP32-S3 DevKitC-1 v1.1
;; with >= 16MB FLASH and >= 8MB PSRAM (memory_type: opi_opi)
platform = ${esp32s3.platform}
board = esp32s3camlcd ;; this is the only standard board with "opi_opi"
board_build.arduino.memory_type = opi_opi
upload_speed = 921600
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32s3.build_flags} -D WLED_RELEASE_NAME=\"ESP32-S3_WROOM-2\"
  -D CONFIG_LITTLEFS_FOR_IDF_3_2 -D WLED_WATCHDOG_TIMEOUT=0
  -D ARDUINO_USB_CDC_ON_BOOT=0  ;; -D ARDUINO_USB_MODE=1 ;; for boards with serial-to-USB chip
  ;; -D ARDUINO_USB_CDC_ON_BOOT=1 -D ARDUINO_USB_MODE=1      ;; for boards with USB-OTG connector only (USBCDC or "TinyUSB")
  -DBOARD_HAS_PSRAM
  -D LEDPIN=38 -D DATA_PINS=38 ;; buildin WS2812b LED
  -D BTNPIN=0 -D RLYPIN=16 -D IRPIN=17 -D AUDIOPIN=-1
  -D WLED_DEBUG
  -D SR_DMTYPE=1 -D I2S_SDPIN=13 -D I2S_CKPIN=14 -D I2S_WSPIN=15 -D MCLK_PIN=4  ;; I2S mic
lib_deps = ${esp32s3.lib_deps}

board_build.partitions = ${esp32.extreme_partitions}
board_upload.flash_size = 16MB
board_upload.maximum_size = 16777216
monitor_filters = esp32_exception_decoder

[env:esp32s3_4M_qspi]
;; ESP32-S3, with 4MB FLASH and <= 4MB PSRAM (memory_type: qio_qspi)
board = lolin_s3_mini ;; -S3 mini, 4MB flash 2MB PSRAM 
platform = ${esp32s3.platform}
upload_speed = 921600
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32s3.build_flags} -D WLED_RELEASE_NAME=\"ESP32-S3_4M_qspi\"
  -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MODE=1      ;; for boards with USB-OTG connector only (USBCDC or "TinyUSB")
  -DBOARD_HAS_PSRAM
  -DLOLIN_WIFI_FIX ; seems to work much better with this
  -D WLED_WATCHDOG_TIMEOUT=0
lib_deps = ${esp32s3.lib_deps}
board_build.partitions = ${esp32.default_partitions}
board_build.f_flash = 80000000L
board_build.flash_mode = qio
monitor_filters = esp32_exception_decoder

[env:lolin_s2_mini]
platform = ${esp32s2.platform}
board = lolin_s2_mini
board_build.partitions = ${esp32.default_partitions}
board_build.flash_mode = qio
board_build.f_flash = 80000000L
custom_usermods = audioreactive
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32s2.build_flags} -D WLED_RELEASE_NAME=\"ESP32-S2\"
  -DARDUINO_USB_CDC_ON_BOOT=1
  -DARDUINO_USB_MSC_ON_BOOT=0
  -DARDUINO_USB_DFU_ON_BOOT=0
  -DBOARD_HAS_PSRAM
  -DLOLIN_WIFI_FIX ; seems to work much better with this
  -D WLED_WATCHDOG_TIMEOUT=0
  -D DATA_PINS=16
  -D HW_PIN_SCL=35
  -D HW_PIN_SDA=33
  -D HW_PIN_CLOCKSPI=7
  -D HW_PIN_DATASPI=11
  -D HW_PIN_MISOSPI=9
;  -D STATUSLED=15
lib_deps = ${esp32s2.lib_deps}


[env:usermods]
board = esp32dev
platform = ${esp32_idf_V4.platform}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp32_idf_V4.build_flags} -D WLED_RELEASE_NAME=\"ESP32_USERMODS\"
  -DTOUCH_CS=9
lib_deps = ${esp32_idf_V4.lib_deps}
monitor_filters = esp32_exception_decoder
board_build.flash_mode = dio
custom_usermods = *   ; Expands to all usermods in usermods folder
board_build.partitions = ${esp32.extreme_partitions}  ; We're gonna need a bigger boat
