{
	"name": "Python 3",
	"build": {
		"dockerfile": "Dockerfile",
		"context": "..",
		"args": { 
			// Update 'VARIANT' to pick a Python version: 3, 3.6, 3.7, 3.8, 3.9
			"VARIANT": "3"
		}
	},

	// To give the container access to a device serial port, you can uncomment one of the following lines.
	// Note: If running on Windows, you will have to do some additional steps:
	// https://stackoverflow.com/questions/68527888/how-can-i-use-a-usb-com-port-inside-of-a-vscode-development-container
	//
	// You can explicitly just forward the port you want to connect to. Replace `/dev/ttyACM0` with the serial port for
	// your device. This will only work if the device is plugged in from the start without reconnecting. Adding the
	// `dialout` group is needed if read/write permisions for the port are limitted to the dialout user.
	// "runArgs": ["--device=/dev/ttyACM0", "--group-add", "dialout"],
	//
	// Alternatively, you can give more comprehensive access to the host system. This will expose all the host devices to
	// the container. Adding the `dialout` group is needed if read/write permisions for the port are limitted to the
	// dialout user. This could allow the container to modify unrelated serial devices, which would be a similar level of
	// risk to running the build directly on the host.
	// "runArgs": ["--privileged", "-v", "/dev/bus/usb:/dev/bus/usb", "--group-add", "dialout"],

	"customizations": {
		"vscode": {
			"settings": { 
				"terminal.integrated.shell.linux": "/bin/bash",
				"python.pythonPath": "/usr/local/bin/python",
				"python.linting.enabled": true,
				"python.linting.pylintEnabled": true,
				"python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
				"python.formatting.blackPath": "/usr/local/py-utils/bin/black",
				"python.formatting.yapfPath": "/usr/local/py-utils/bin/yapf",
				"python.linting.banditPath": "/usr/local/py-utils/bin/bandit",
				"python.linting.flake8Path": "/usr/local/py-utils/bin/flake8",
				"python.linting.mypyPath": "/usr/local/py-utils/bin/mypy",
				"python.linting.pycodestylePath": "/usr/local/py-utils/bin/pycodestyle",
				"python.linting.pydocstylePath": "/usr/local/py-utils/bin/pydocstyle",
				"python.linting.pylintPath": "/usr/local/py-utils/bin/pylint"
			},
			"extensions": [
				"ms-python.python",
				"platformio.platformio-ide"
			]
		}
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "bash -i -c 'nvm install && npm ci'",

	// Comment out connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode"
}

