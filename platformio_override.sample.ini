# Example PlatformIO Project Configuration Override
# ------------------------------------------------------------------------------
# Copy to platformio_override.ini to activate overrides
# ------------------------------------------------------------------------------
# Please visit documentation: https://docs.platformio.org/page/projectconf.html

[platformio]
default_envs = WLED_generic8266_1M, esp32dev_V4_dio80  # put the name(s) of your own build environment here. You can define as many as you need

#----------
# SAMPLE
#----------
[env:WLED_generic8266_1M]
extends = env:esp01_1m_full  # when you want to extend the existing environment (define only updated options)
; board = esp01_1m  # uncomment when ou need different board
; platform = ${common.platform_wled_default}  # uncomment and change when you want particular platform
; platform_packages = ${common.platform_packages}
; board_build.ldscript = ${common.ldscript_1m128k}
; upload_speed = 921600 # fast upload speed (remove ';' if your board supports fast upload speed)
# Sample libraries used for various usermods. Uncomment when using particular usermod.
lib_deps = ${esp8266.lib_deps}
;  olikraus/U8g2 # @~2.33.15
;  paulstoffregen/OneWire@~2.3.8
;  adafruit/Adafruit Unified Sensor@^1.1.4
;  adafruit/DHT sensor library@^1.4.1
;  adafruit/Adafruit BME280 Library@^2.2.2
;  Wire
;  robtillaart/SHT85@~0.3.3
;  ;gmag11/QuickESPNow @ ~0.7.0 # will also load QuickDebug
;  https://github.com/blazoncek/QuickESPNow.git#optional-debug  ;; exludes debug library
;  ${esp32.AR_lib_deps} ;; needed for USERMOD_AUDIOREACTIVE

build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
;
; *** To use the below defines/overrides, copy and paste each onto its own line just below build_flags in the section above.
; 
; Set a release name that may be used to distinguish required binary for flashing
;   -D WLED_RELEASE_NAME=\"ESP32_MULTI_USREMODS\"
;
; disable specific features
;   -D WLED_DISABLE_OTA
;   -D WLED_DISABLE_ALEXA
;   -D WLED_DISABLE_HUESYNC
;   -D WLED_DISABLE_LOXONE
;   -D WLED_DISABLE_INFRARED
;   -D WLED_DISABLE_WEBSOCKETS
;   -D WLED_DISABLE_MQTT
;   -D WLED_DISABLE_ADALIGHT
;   -D WLED_DISABLE_2D
;   -D WLED_DISABLE_PXMAGIC
;   -D WLED_DISABLE_ESPNOW
;   -D WLED_DISABLE_BROWNOUT_DET
;
; enable optional built-in features
;   -D WLED_ENABLE_PIXART
;   -D WLED_ENABLE_USERMOD_PAGE # if created
;   -D WLED_ENABLE_DMX
;
; PIN defines - uncomment and change, if needed:
;   -D DATA_PINS=2
; or use this for multiple outputs
;   -D DATA_PINS=1,3
;   -D BTNPIN=0
;   -D IRPIN=4
;   -D RLYPIN=12
;   -D RLYMDE=1
;   -D RLYODRAIN=0
;   -D LED_BUILTIN=2 # GPIO of built-in LED
;
; Limit max buses
;   -D WLED_MAX_BUSSES=2
;   -D WLED_MAX_ANALOG_CHANNELS=3   # only 3 PWM HW pins available
;   -D WLED_MAX_DIGITAL_CHANNELS=2  # only 2 HW accelerated pins available
;
; Configure default WiFi
;   -D CLIENT_SSID='"MyNetwork"'
;   -D CLIENT_PASS='"Netw0rkPassw0rd"'
;
; Configure and use Ethernet
;   -D WLED_USE_ETHERNET
;   -D WLED_ETH_DEFAULT=5
; do not use pins 5, (16,) 17, 18, 19, 21, 22, 23, 25, 26, 27 for anything but ethernet
;   -D PHY_ADDR=0 -D ETH_PHY_POWER=5 -D ETH_PHY_MDC=23 -D ETH_PHY_MDIO=18
;   -D ETH_CLK_MODE=ETH_CLOCK_GPIO17_OUT
;
; NTP time configuration
;   -D WLED_NTP_ENABLED=true
;   -D WLED_TIMEZONE=2
;   -D WLED_LAT=48.86
;   -D WLED_LON=2.33
;
; Use Watchdog timer with 10s guard
;   -D WLED_WATCHDOG_TIMEOUT=10
;
; Create debug build (with remote debug)
;   -D WLED_DEBUG
;   -D WLED_DEBUG_HOST='"*************"'
;   -D WLED_DEBUG_PORT=7868
;
; Use Autosave usermod and set it to do save after 90s
;   -D USERMOD_AUTO_SAVE
;   -D AUTOSAVE_AFTER_SEC=90
;
; Use AHT10/AHT15/AHT20 usermod
;   -D USERMOD_AHT10
;
; Use INA226 usermod
;   -D USERMOD_INA226
;
; Use 4 Line Display usermod with SPI display
;   -D USERMOD_FOUR_LINE_DISPLAY
;   -DFLD_SPI_DEFAULT
;   -D FLD_TYPE=SSD1306_SPI64
;   -D FLD_PIN_CLOCKSPI=14
;   -D FLD_PIN_DATASPI=13
;   -D FLD_PIN_DC=26
;   -D FLD_PIN_CS=15
;   -D FLD_PIN_RESET=27
;
; Use Rotary encoder usermod (in conjunction with 4LD)
;   -D USERMOD_ROTARY_ENCODER_UI
;   -D ENCODER_DT_PIN=5
;   -D ENCODER_CLK_PIN=18
;   -D ENCODER_SW_PIN=19
;
; Use Dallas DS18B20 temperature sensor usermod and configure it to use GPIO13
;   -D USERMOD_DALLASTEMPERATURE
;   -D TEMPERATURE_PIN=13
;
; Use Multi Relay usermod and configure it to use 6 relays and appropriate GPIO
;   -D USERMOD_MULTI_RELAY
;   -D MULTI_RELAY_MAX_RELAYS=6
;   -D MULTI_RELAY_PINS=12,23,22,21,24,25
;
; Use PIR sensor usermod and configure it to use GPIO4 and timer of 60s
;   -D USERMOD_PIRSWITCH
;   -D PIR_SENSOR_PIN=4   # use -1 to disable usermod
;   -D PIR_SENSOR_OFF_SEC=60
;   -D PIR_SENSOR_MAX_SENSORS=2 # max allowable sensors (uses OR logic for triggering)
;
; Use Audioreactive usermod and configure I2S microphone
;   ${esp32.AR_build_flags} ;; default flags required to properly configure ArduinoFFT
;   ;; don't forget to add ArduinoFFT to your libs_deps: ${esp32.AR_lib_deps}
;   -D AUDIOPIN=-1
;   -D DMTYPE=1     # 0-analog/disabled, 1-I2S generic, 2-ES7243, 3-SPH0645, 4-I2S+mclk, 5-I2S PDM
;   -D I2S_SDPIN=36
;   -D I2S_WSPIN=23
;   -D I2S_CKPIN=19
;
; Use PWM fan usermod
;   -D USERMOD_PWM_FAN
;   -D TACHO_PIN=33
;   -D PWM_PIN=32
;
;  Use POV Display usermod
;   -D USERMOD_POV_DISPLAY
; Use built-in or custom LED as a status indicator (assumes LED is connected to GPIO16)
;   -D STATUSLED=16
;
; set the name of the module - make sure there is a quote-backslash-quote before the name and a backslash-quote-quote after the name
;   -D SERVERNAME="\"WLED\""
;
; set the number of LEDs
;   -D PIXEL_COUNTS=30
; or this for multiple outputs
;   -D PIXEL_COUNTS=30,30
;
; set the default LED type
;   -D LED_TYPES=22    # see const.h (TYPE_xxxx)
; or this for multiple outputs
;   -D LED_TYPES=TYPE_SK6812_RGBW,TYPE_WS2812_RGB
;
; set default color order of your led strip
;   -D DEFAULT_LED_COLOR_ORDER=COL_ORDER_GRB
;
; set milliampere limit when using ESP power pin (or inadequate PSU) to power LEDs
;   -D ABL_MILLIAMPS_DEFAULT=850
;   -D LED_MILLIAMPS_DEFAULT=55
;
; enable IR by setting remote type
;   -D IRTYPE=0   # 0 Remote disabled | 1 24-key RGB | 2 24-key with CT | 3 40-key blue | 4 40-key RGB | 5 21-key RGB | 6 6-key black | 7 9-key red | 8 JSON remote
;   
; use PSRAM on classic ESP32 rev.1 (rev.3 or above has no issues)
;   -DBOARD_HAS_PSRAM -mfix-esp32-psram-cache-issue   # needed only for classic ESP32 rev.1
;
; configure I2C and SPI interface (for various hardware)
;   -D I2CSDAPIN=33 # initialise interface
;   -D I2CSCLPIN=35 # initialise interface
;   -D HW_PIN_SCL=35
;   -D HW_PIN_SDA=33
;   -D HW_PIN_CLOCKSPI=7
;   -D HW_PIN_DATASPI=11
;   -D HW_PIN_MISOSPI=9



# ------------------------------------------------------------------------------
# PRE-CONFIGURED DEVELOPMENT BOARDS AND CONTROLLERS
# ------------------------------------------------------------------------------

[env:esp07]
board = esp07
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
lib_deps = ${esp8266.lib_deps}

[env:d1_mini]
board = d1_mini
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
upload_speed = 921600
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
lib_deps = ${esp8266.lib_deps}
monitor_filters = esp8266_exception_decoder

[env:heltec_wifi_kit_8]
board = d1_mini
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
lib_deps = ${esp8266.lib_deps}

[env:h803wf]
board = d1_mini
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D DATA_PINS=1 -D WLED_DISABLE_INFRARED
lib_deps = ${esp8266.lib_deps}

[env:esp32dev_qio80]
extends = env:esp32dev  # we want to extend the existing esp32dev environment (and define only updated options)
board = esp32dev
build_flags = ${common.build_flags} ${esp32.build_flags} #-D WLED_DISABLE_BROWNOUT_DET
  ${esp32.AR_build_flags} ;; optional - includes USERMOD_AUDIOREACTIVE
lib_deps = ${esp32.lib_deps}
  ${esp32.AR_lib_deps} ;; needed for USERMOD_AUDIOREACTIVE
monitor_filters = esp32_exception_decoder
board_build.f_flash = 80000000L
board_build.flash_mode = qio

[env:esp32dev_V4_dio80]
;; experimental ESP32 env using ESP-IDF V4.4.x
;; Warning: this build environment is not stable!!
;; please erase your device before installing.
extends = esp32_idf_V4  # based on newer "esp-idf V4" platform environment
board = esp32dev
build_flags = ${common.build_flags}  ${esp32_idf_V4.build_flags} #-D WLED_DISABLE_BROWNOUT_DET
  ${esp32.AR_build_flags} ;; includes USERMOD_AUDIOREACTIVE
lib_deps = ${esp32_idf_V4.lib_deps}
  ${esp32.AR_lib_deps} ;; needed for USERMOD_AUDIOREACTIVE
monitor_filters = esp32_exception_decoder
board_build.partitions = ${esp32.default_partitions}  ;; if you get errors about "out of program space", change this to ${esp32.extended_partitions} or even ${esp32.big_partitions}
board_build.f_flash = 80000000L
board_build.flash_mode = dio

[env:esp32s2_saola]
extends = esp32s2
board = esp32-s2-saola-1
platform = ${esp32s2.platform}
platform_packages = ${esp32s2.platform_packages}
framework = arduino
board_build.flash_mode = qio
upload_speed = 460800
build_flags = ${common.build_flags} ${esp32s2.build_flags}
  ;-DLOLIN_WIFI_FIX ;; try this in case Wifi does not work
  -DARDUINO_USB_CDC_ON_BOOT=1
lib_deps = ${esp32s2.lib_deps}

[env:esp32s3dev_8MB_PSRAM_qspi]
;; ESP32-TinyS3 development board, with 8MB FLASH and PSRAM (memory_type: qio_qspi)
extends = env:esp32s3dev_8MB_PSRAM_opi
;board = um_tinys3 ;    -> needs workaround from https://github.com/wled-dev/WLED/pull/2905#issuecomment-1328049860
board = esp32-s3-devkitc-1 ;; generic dev board; the next line adds PSRAM support
board_build.arduino.memory_type = qio_qspi ;; use with PSRAM: 2MB or  4MB

[env:esp8285_4CH_MagicHome]
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_1m128k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_DISABLE_OTA
lib_deps = ${esp8266.lib_deps}

[env:esp8285_H801]
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_1m128k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_DISABLE_OTA
lib_deps = ${esp8266.lib_deps}

[env:d1_mini_5CH_Shojo_PCB]
board = d1_mini
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_USE_SHOJO_PCB ;; NB: WLED_USE_SHOJO_PCB is not used anywhere in the source code. Not sure why its needed.
lib_deps = ${esp8266.lib_deps}

[env:d1_mini_debug]
board = d1_mini
build_type = debug
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} ${common.debug_flags}
lib_deps = ${esp8266.lib_deps}

[env:d1_mini_ota]
board = d1_mini
upload_protocol = espota
# exchange for your WLED IP
upload_port = "**********"
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
lib_deps = ${esp8266.lib_deps}

[env:anavi_miracle_controller]
board = d1_mini
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D DATA_PINS=12 -D IRPIN=-1 -D RLYPIN=2
lib_deps = ${esp8266.lib_deps}

[env:esp32c3dev_2MB]
;; for ESP32-C3 boards with 2MB flash (instead of 4MB).
;; this board need a specific partition file. OTA not possible.
extends = esp32c3
platform = ${esp32c3.platform}
platform_packages = ${esp32c3.platform_packages}
board = esp32-c3-devkitm-1
build_flags = ${common.build_flags} ${esp32c3.build_flags}
  -D WLED_WATCHDOG_TIMEOUT=0
  -D WLED_DISABLE_OTA
  ; -DARDUINO_USB_CDC_ON_BOOT=1 ;; for virtual CDC USB
  -DARDUINO_USB_CDC_ON_BOOT=0   ;; for serial-to-USB chip
build_unflags = ${common.build_unflags}
upload_speed = 115200
lib_deps = ${esp32c3.lib_deps}
board_build.partitions = tools/WLED_ESP32_2MB_noOTA.csv
board_build.flash_mode = dio
board_upload.flash_size = 2MB
board_upload.maximum_size = 2097152

[env:wemos_shield_esp32]
extends = esp32              ;; use default esp32 platform
board = esp32dev
upload_speed = 460800
build_flags = ${common.build_flags} ${esp32.build_flags}
  -D WLED_RELEASE_NAME=\"ESP32_wemos_shield\"
  -D DATA_PINS=16
  -D RLYPIN=19
  -D BTNPIN=17
  -D IRPIN=18
  -UWLED_USE_MY_CONFIG
  -D USERMOD_DALLASTEMPERATURE
  -D USERMOD_FOUR_LINE_DISPLAY
  -D TEMPERATURE_PIN=23
  ${esp32.AR_build_flags} ;; includes USERMOD_AUDIOREACTIVE
lib_deps = ${esp32.lib_deps}
  OneWire@~2.3.5          ;; needed for USERMOD_DALLASTEMPERATURE
  olikraus/U8g2 @ ^2.28.8 ;; needed for USERMOD_FOUR_LINE_DISPLAY
  ${esp32.AR_lib_deps}    ;; needed for USERMOD_AUDIOREACTIVE
board_build.partitions = ${esp32.default_partitions}

[env:esp32_pico-D4]
extends = esp32              ;; use default esp32 platform
board = pico32               ;; pico32-D4 is different from the standard esp32dev
                             ;; hardware details from https://github.com/srg74/WLED-ESP32-pico
build_flags = ${common.build_flags} ${esp32.build_flags}
  -D WLED_RELEASE_NAME=\"pico32-D4\" -D SERVERNAME='"WLED-pico32"'
  -D WLED_DISABLE_ADALIGHT   ;; no serial-to-USB chip on this board - better to disable serial protocols
  -D DATA_PINS=2,18          ;; LED pins
  -D RLYPIN=19 -D BTNPIN=0 -D IRPIN=-1 ;; no default pin for IR
  ${esp32.AR_build_flags}    ;; include USERMOD_AUDIOREACTIVE
  -D UM_AUDIOREACTIVE_ENABLE ;; enable AR by default
  ;; Audioreactive settings for on-board microphone (ICS-43432)
  -D SR_DMTYPE=1 -D I2S_SDPIN=25 -D I2S_WSPIN=15 -D I2S_CKPIN=14
  -D SR_SQUELCH=5 -D SR_GAIN=30
lib_deps = ${esp32.lib_deps}
  ${esp32.AR_lib_deps}       ;; needed for USERMOD_AUDIOREACTIVE
board_build.partitions = ${esp32.default_partitions}
board_build.f_flash = 80000000L

[env:m5atom]
extends = env:esp32dev  # we want to extend the existing esp32dev environment (and define only updated options)
build_flags = ${common.build_flags} ${esp32.build_flags} -D DATA_PINS=27 -D BTNPIN=39

[env:sp501e]
board = esp_wroom_02
platform = ${common.platform_wled_default}
board_build.ldscript = ${common.ldscript_2m512k}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D DATA_PINS=3 -D BTNPIN=1
lib_deps = ${esp8266.lib_deps}

[env:sp511e]
board = esp_wroom_02
platform = ${common.platform_wled_default}
board_build.ldscript = ${common.ldscript_2m512k}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D DATA_PINS=3 -D BTNPIN=2 -D IRPIN=5 -D WLED_MAX_BUTTONS=3
lib_deps = ${esp8266.lib_deps}

[env:Athom_RGBCW]        ;7w and 5w(GU10) bulbs
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D BTNPIN=-1 -D RLYPIN=-1 -D DATA_PINS=4,12,14,13,5
                                            -D LED_TYPES=TYPE_ANALOG_5CH -D WLED_DISABLE_INFRARED -D WLED_MAX_CCT_BLEND=0
lib_deps = ${esp8266.lib_deps}

[env:Athom_15w_RGBCW]        ;15w bulb
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D BTNPIN=-1 -D RLYPIN=-1 -D DATA_PINS=4,12,14,5,13
                                            -D LED_TYPES=TYPE_ANALOG_5CH -D WLED_DISABLE_INFRARED -D WLED_MAX_CCT_BLEND=0 -D WLED_USE_IC_CCT
lib_deps = ${esp8266.lib_deps}

[env:Athom_3Pin_Controller]        ;small controller with only data
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D BTNPIN=0 -D RLYPIN=-1 -D DATA_PINS=1 -D WLED_DISABLE_INFRARED
lib_deps = ${esp8266.lib_deps}

[env:Athom_4Pin_Controller]       ; With clock and data interface
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D BTNPIN=0 -D RLYPIN=12 -D DATA_PINS=1 -D WLED_DISABLE_INFRARED
lib_deps = ${esp8266.lib_deps}

[env:Athom_5Pin_Controller]      ;Analog light strip controller
board = esp8285
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D BTNPIN=0 -D RLYPIN=-1 DATA_PINS=4,12,14,13 -D WLED_DISABLE_INFRARED
lib_deps = ${esp8266.lib_deps}

[env:MY9291]
board = esp01_1m
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_1m128k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags} -D WLED_DISABLE_OTA -D USERMOD_MY9291
lib_deps = ${esp8266.lib_deps}

# ------------------------------------------------------------------------------
# codm pixel controller board configurations
# codm-controller-0_6 can also be used for the TYWE3S controller
# ------------------------------------------------------------------------------

[env:codm-controller-0_6]
board = esp_wroom_02
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_2m512k}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
lib_deps = ${esp8266.lib_deps}

[env:codm-controller-0_6-rev2]
board = esp_wroom_02
platform = ${common.platform_wled_default}
platform_packages = ${common.platform_packages}
board_build.ldscript = ${common.ldscript_4m1m}
build_unflags = ${common.build_unflags}
build_flags = ${common.build_flags} ${esp8266.build_flags}
lib_deps = ${esp8266.lib_deps}

# ------------------------------------------------------------------------------
# EleksTube-IPS
# ------------------------------------------------------------------------------
[env:elekstube_ips]
extends = esp32              ;; use default esp32 platform
board = esp32dev
upload_speed = 921600
custom_usermods = ${env:esp32dev.custom_usermods} RTC EleksTube_IPS
build_flags = ${common.build_flags} ${esp32.build_flags} -D WLED_DISABLE_BROWNOUT_DET -D WLED_DISABLE_INFRARED
  -D DATA_PINS=12
  -D RLYPIN=27
  -D BTNPIN=34
  -D PIXEL_COUNTS=6
  # Display config
  -D ST7789_DRIVER
  -D TFT_WIDTH=135
  -D TFT_HEIGHT=240
  -D CGRAM_OFFSET
  -D TFT_SDA_READ
  -D TFT_MOSI=23
  -D TFT_SCLK=18
  -D TFT_DC=25
  -D TFT_RST=26
  -D SPI_FREQUENCY=40000000
  -D USER_SETUP_LOADED
monitor_filters = esp32_exception_decoder

# ------------------------------------------------------------------------------
# Usermod examples
# ------------------------------------------------------------------------------

# 433MHz RF remote example for esp32dev
[env:esp32dev_usermod_RF433]
extends = env:esp32dev
build_flags = ${env:esp32dev.build_flags} -D USERMOD_RF433
lib_deps = ${env:esp32dev.lib_deps}
  sui77/rc-switch @ 2.6.4
